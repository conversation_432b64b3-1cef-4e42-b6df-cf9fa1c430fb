@echo off
:: Kitco Research AI - Startup Script for Windows
:: This script activates the virtual environment and starts the web application

echo 🚀 Starting Kitco Research AI...

:: Find project root directory
call :find_project_root
cd /d "%PROJECT_ROOT%"

echo 📁 Project Root: %PROJECT_ROOT%
echo.

:: Check if virtual environment exists
if not exist ".venv" (
    echo ❌ Virtual environment not found!
    echo 🔧 Please run the initial setup first:
    echo    scripts\setup.bat
    exit /b 1
)

:: Check if setup is complete
if not exist ".env" (
    echo ❌ Configuration not found!
    echo 🔧 Please run the initial setup first:
    echo    scripts\setup.bat
    exit /b 1
)

:: Activate virtual environment
echo 🔄 Activating virtual environment...
call .venv\Scripts\activate.bat
echo ✅ Virtual environment activated
echo.

:: Set default port if not specified
if "%~1"=="" (
    set PORT=8765
) else (
    set PORT=%~1
)

:: Check if port is in use (Windows equivalent)
netstat -an | findstr ":%PORT% " | findstr "LISTENING" >nul 2>&1
if not errorlevel 1 (
    echo ⚠️ Port %PORT% is already in use!
    echo 🔄 Try using a different port:
    echo    scripts\start_app.bat 5002
    echo.
    echo 🔄 Or use the restart script to clean up:
    echo    scripts\restart.bat
    exit /b 1
)

echo 📡 Starting web server on port %PORT%...
echo 🌐 Access the application at: http://localhost:%PORT%
echo ⏹️ Press Ctrl+C to stop the server
echo.

:: Start the application
python app.py --port=%PORT%

exit /b 0

:: Function to find project root directory
:find_project_root
set "CURRENT_DIR=%CD%"

:: Check if we're already in project root (has app.py and src/ directory)
if exist "app.py" if exist "src\kitco_research_ai" (
    set "PROJECT_ROOT=%CD%"
    exit /b 0
)

:: Search upward for project root
set "SEARCH_DIR=%CURRENT_DIR%"
:search_loop
if "%SEARCH_DIR%"=="" goto search_failed
if exist "%SEARCH_DIR%\app.py" if exist "%SEARCH_DIR%\src\kitco_research_ai" (
    set "PROJECT_ROOT=%SEARCH_DIR%"
    exit /b 0
)
for %%I in ("%SEARCH_DIR%\.") do set "PARENT_DIR=%%~dpI"
set "PARENT_DIR=%PARENT_DIR:~0,-1%"
if "%PARENT_DIR%"=="%SEARCH_DIR%" goto search_failed
set "SEARCH_DIR=%PARENT_DIR%"
goto search_loop

:search_failed
echo ❌ Error: Could not find project root directory
echo    Please run this script from the kitco_research_ai project directory
exit /b 1
