#!/bin/bash
# ============================================================================
# Kitco Research AI - Docker Container Testing Script
# ============================================================================
# Comprehensive testing script for containerized application
# Tests connectivity, health checks, and basic functionality
# ============================================================================

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
MAIN_APP_URL="http://localhost:8765"
SEARXNG_URL="http://localhost:8080"
TIMEOUT=30
RETRY_COUNT=5
RETRY_DELAY=5

# Test results
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test_result() {
    local test_name="$1"
    local result="$2"
    local message="$3"
    
    if [[ "$result" = "PASS" ]]; then
        echo -e "${GREEN}[PASS]${NC} $test_name: $message"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}[FAIL]${NC} $test_name: $message"
        ((TESTS_FAILED++))
        FAILED_TESTS+=("$test_name")
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name="$1"
    local url="$2"
    local max_attempts="$3"
    
    print_status "Waiting for $service_name to be ready..."
    
    for ((i=1; i<=max_attempts; i++)); do
        if curl -s -f "$url" > /dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        if [[ $i -lt $max_attempts ]]; then
            print_status "Attempt $i/$max_attempts failed, retrying in ${RETRY_DELAY}s..."
            sleep $RETRY_DELAY
        fi
    done
    
    print_error "$service_name failed to become ready after $max_attempts attempts"
    return 1
}

# Function to test container status
test_container_status() {
    print_status "Testing container status..."
    
    # Check if containers are running
    local containers=(
        "kitco-searxng"
        "kitco-research-ai-dev"
    )
    
    for container in "${containers[@]}"; do
        if docker ps --filter "name=$container" --filter "status=running" | grep -q "$container"; then
            print_test_result "Container Status" "PASS" "$container is running"
        else
            print_test_result "Container Status" "FAIL" "$container is not running"
        fi
    done
}

# Function to test health checks
test_health_checks() {
    print_status "Testing health checks..."
    
    # Wait for services to be ready
    if wait_for_service "SearXNG" "$SEARXNG_URL" $RETRY_COUNT; then
        print_test_result "SearXNG Health" "PASS" "SearXNG is responding"
    else
        print_test_result "SearXNG Health" "FAIL" "SearXNG is not responding"
    fi
    
    if wait_for_service "Main Application" "$MAIN_APP_URL/health" $RETRY_COUNT; then
        print_test_result "App Health" "PASS" "Main application health check passed"
    else
        print_test_result "App Health" "FAIL" "Main application health check failed"
    fi
}

# Function to test network connectivity
test_network_connectivity() {
    print_status "Testing network connectivity..."
    
    # Test if main app can reach SearXNG
    local network_test_result
    network_test_result=$(docker exec kitco-research-ai-dev curl -s -f http://searxng:8080 > /dev/null 2>&1 && echo "PASS" || echo "FAIL")
    
    if [[ "$network_test_result" = "PASS" ]]; then
        print_test_result "Network Connectivity" "PASS" "Main app can reach SearXNG container"
    else
        print_test_result "Network Connectivity" "FAIL" "Main app cannot reach SearXNG container"
    fi
}

# Function to test SearXNG search functionality
test_searxng_search() {
    print_status "Testing SearXNG search functionality..."

    # Test basic search
    local search_result
    search_result=$(curl -s -f "$SEARXNG_URL/search?q=test&format=json" | jq -r '.results | length' 2>/dev/null || echo "0")

    if [[ "$search_result" -gt 0 ]]; then
        print_test_result "SearXNG Search" "PASS" "SearXNG returned $search_result search results"
    else
        print_test_result "SearXNG Search" "FAIL" "SearXNG search returned no results or failed"
    fi
}

# Function to test LM Studio connectivity
test_lmstudio_connectivity() {
    print_status "Testing LM Studio connectivity..."

    # Test if container can reach LM Studio on host
    local lmstudio_test_result
    lmstudio_test_result=$(docker exec kitco-research-ai-dev curl -s -f http://host.docker.internal:1234/v1/models > /dev/null 2>&1 && echo "PASS" || echo "FAIL")

    if [[ "$lmstudio_test_result" = "PASS" ]]; then
        print_test_result "LM Studio Connectivity" "PASS" "Container can reach LM Studio on host machine"

        # Try to get available models
        local models_info
        models_info=$(docker exec kitco-research-ai-dev curl -s http://host.docker.internal:1234/v1/models 2>/dev/null | jq -r '.data | length' 2>/dev/null || echo "0")

        if [[ "$models_info" -gt 0 ]]; then
            print_test_result "LM Studio Models" "PASS" "LM Studio has $models_info model(s) available"
        else
            print_test_result "LM Studio Models" "FAIL" "LM Studio is reachable but no models found"
        fi
    else
        print_test_result "LM Studio Connectivity" "FAIL" "Container cannot reach LM Studio (is it running on localhost:1234?)"
    fi
}

# Function to test main application endpoints
test_application_endpoints() {
    print_status "Testing main application endpoints..."
    
    # Test main page
    if curl -s -f "$MAIN_APP_URL" > /dev/null 2>&1; then
        print_test_result "Main Page" "PASS" "Main application page is accessible"
    else
        print_test_result "Main Page" "FAIL" "Main application page is not accessible"
    fi
    
    # Test API health endpoint
    local health_response
    health_response=$(curl -s "$MAIN_APP_URL/health" 2>/dev/null || echo "")
    
    if [[ "$health_response" =~ "healthy" ]] || [[ "$health_response" =~ "ok" ]]; then
        print_test_result "Health Endpoint" "PASS" "Health endpoint returned positive response"
    else
        print_test_result "Health Endpoint" "FAIL" "Health endpoint returned unexpected response: $health_response"
    fi
}

# Function to test environment variables
test_environment_variables() {
    print_status "Testing environment variables..."

    # Check if SEARXNG_URL is set correctly in the container
    local searxng_url_in_container
    searxng_url_in_container=$(docker exec kitco-research-ai-dev printenv SEARXNG_URL 2>/dev/null || echo "")

    if [[ "$searxng_url_in_container" = "http://searxng:8080" ]]; then
        print_test_result "SearXNG URL" "PASS" "SEARXNG_URL is correctly set to $searxng_url_in_container"
    else
        print_test_result "SearXNG URL" "FAIL" "SEARXNG_URL is not set correctly (got: $searxng_url_in_container)"
    fi

    # Check if LM_STUDIO_URL is set correctly in the container
    local lmstudio_url_in_container
    lmstudio_url_in_container=$(docker exec kitco-research-ai-dev printenv LM_STUDIO_URL 2>/dev/null || echo "")

    if [[ "$lmstudio_url_in_container" = "http://host.docker.internal:1234" ]]; then
        print_test_result "LM Studio URL" "PASS" "LM_STUDIO_URL is correctly set to $lmstudio_url_in_container"
    else
        print_test_result "LM Studio URL" "FAIL" "LM_STUDIO_URL is not set correctly (got: $lmstudio_url_in_container)"
    fi
}

# Function to test volume mounts
test_volume_mounts() {
    print_status "Testing volume mounts..."
    
    # Check if data directory is mounted
    if docker exec kitco-research-ai-dev test -d /app/data; then
        print_test_result "Volume Mounts" "PASS" "Data directory is properly mounted"
    else
        print_test_result "Volume Mounts" "FAIL" "Data directory is not mounted"
    fi
    
    # Check if logs directory is mounted
    if docker exec kitco-research-ai-dev test -d /app/logs; then
        print_test_result "Log Volume" "PASS" "Logs directory is properly mounted"
    else
        print_test_result "Log Volume" "FAIL" "Logs directory is not mounted"
    fi
}

# Function to test resource usage
test_resource_usage() {
    print_status "Testing resource usage..."
    
    # Get container stats
    local stats
    stats=$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep -E "(kitco-|searxng)")
    
    if [[ -n "$stats" ]]; then
        print_test_result "Resource Usage" "PASS" "Container resource stats available"
        echo "$stats"
    else
        print_test_result "Resource Usage" "FAIL" "Could not retrieve container resource stats"
    fi
}

# Function to show test summary
show_test_summary() {
    echo
    echo "============================================================================"
    echo "                           TEST SUMMARY"
    echo "============================================================================"
    echo -e "Tests Passed: ${GREEN}$TESTS_PASSED${NC}"
    echo -e "Tests Failed: ${RED}$TESTS_FAILED${NC}"
    echo -e "Total Tests:  $((TESTS_PASSED + TESTS_FAILED))"
    
    if [[ $TESTS_FAILED -gt 0 ]]; then
        echo
        echo -e "${RED}Failed Tests:${NC}"
        for test in "${FAILED_TESTS[@]}"; do
            echo -e "  ${RED}•${NC} $test"
        done
        echo
        print_error "Some tests failed. Please check the output above for details."
        return 1
    else
        echo
        print_success "All tests passed! The containerized application is working correctly."
        return 0
    fi
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Test Kitco Research AI Docker containers

OPTIONS:
    -h, --help              Show this help message

This script tests:
    • Container status and health checks
    • Network connectivity between containers
    • SearXNG search functionality
    • Main application endpoints
    • Environment variable configuration
    • Volume mounts
    • Resource usage

PREREQUISITES:
    • Docker containers must be running
    • Use 'scripts/docker-start.sh' to start containers first

EOF
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed or not in PATH"
        exit 1
    fi
    
    # Check if curl is available
    if ! command -v curl &> /dev/null; then
        print_error "curl is not installed or not in PATH"
        exit 1
    fi
    
    # Check if jq is available (optional, for JSON parsing)
    if ! command -v jq &> /dev/null; then
        print_warning "jq is not installed. Some tests may be limited."
    fi
    
    print_success "Prerequisites check passed"
}

# Main execution
main() {
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_status "Starting Kitco Research AI Docker container tests..."
    echo
    
    check_prerequisites
    test_container_status
    test_health_checks
    test_network_connectivity
    test_environment_variables
    test_volume_mounts
    test_searxng_search
    test_lmstudio_connectivity
    test_application_endpoints
    test_resource_usage
    
    echo
    show_test_summary
}

# Run main function with all arguments
main "$@"
