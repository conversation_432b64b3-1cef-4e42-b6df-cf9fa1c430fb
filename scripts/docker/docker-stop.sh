#!/bin/bash
# ============================================================================
# Kitco Research AI - Docker Container Stop Script
# ============================================================================
# Comprehensive script for stopping and cleaning up containerized application
# ============================================================================

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="all"
REMOVE_VOLUMES=false
REMOVE_IMAGES=false
FORCE_STOP=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Stop Kitco Research AI Docker containers

OPTIONS:
    -e, --env ENVIRONMENT    Environment to stop (dev|prod|all) [default: all]
    -v, --volumes           Remove volumes (WARNING: This deletes all data!)
    -i, --images            Remove application images
    -f, --force             Force stop containers (kill instead of graceful stop)
    -h, --help              Show this help message

EXAMPLES:
    $0                      # Stop all containers
    $0 -e dev               # Stop only development containers
    $0 -e prod              # Stop only production containers
    $0 -v                   # Stop containers and remove volumes (data loss!)
    $0 -i                   # Stop containers and remove images
    $0 -f                   # Force stop all containers

WARNING:
    Using -v/--volumes will permanently delete all application data!

EOF
}

# Function to parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -v|--volumes)
                REMOVE_VOLUMES=true
                shift
                ;;
            -i|--images)
                REMOVE_IMAGES=true
                shift
                ;;
            -f|--force)
                FORCE_STOP=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Function to validate environment
validate_environment() {
    if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" && "$ENVIRONMENT" != "all" ]]; then
        print_error "Invalid environment: $ENVIRONMENT. Must be 'dev', 'prod', or 'all'"
        exit 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed."
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available."
        exit 1
    fi
    
    # Check if compose file exists
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        print_error "Docker Compose file not found: $COMPOSE_FILE"
        exit 1
    fi
}

# Function to confirm destructive operations
confirm_destructive_action() {
    if [[ "$REMOVE_VOLUMES" = true ]]; then
        print_warning "WARNING: This will permanently delete all application data!"
        echo -n "Are you sure you want to continue? (yes/no): "
        read -r response
        if [[ "$response" != "yes" ]]; then
            print_status "Operation cancelled."
            exit 0
        fi
    fi
}

# Function to stop services
stop_services() {
    print_status "Stopping Kitco Research AI containers..."
    
    if [[ "$ENVIRONMENT" = "all" ]]; then
        # Stop all services
        if [[ "$FORCE_STOP" = true ]]; then
            docker compose kill
        else
            docker compose down
        fi
    else
        # Stop specific environment
        if [[ "$FORCE_STOP" = true ]]; then
            docker compose --profile "$ENVIRONMENT" kill
        else
            docker compose --profile "$ENVIRONMENT" down
        fi
    fi
    
    print_success "Containers stopped successfully"
}

# Function to remove volumes if requested
remove_volumes() {
    if [[ "$REMOVE_VOLUMES" = true ]]; then
        print_status "Removing volumes..."
        
        # Remove named volumes
        local volumes=(
            "kitco_research_ai_searxng_data"
        )
        
        for volume in "${volumes[@]}"; do
            if docker volume ls -q | grep -q "^${volume}$"; then
                print_status "Removing volume: $volume"
                docker volume rm "$volume" || print_warning "Failed to remove volume: $volume"
            fi
        done
        
        print_success "Volumes removed"
    fi
}

# Function to remove images if requested
remove_images() {
    if [[ "$REMOVE_IMAGES" = true ]]; then
        print_status "Removing application images..."
        
        # Get image names from docker-compose
        local images=(
            "kitco_research_ai-kitco-research-ai-dev"
            "kitco_research_ai-kitco-research-ai-prod"
        )
        
        for image in "${images[@]}"; do
            if docker images -q "$image" &> /dev/null; then
                print_status "Removing image: $image"
                docker rmi "$image" || print_warning "Failed to remove image: $image"
            fi
        done
        
        print_success "Application images removed"
    fi
}

# Function to show final status
show_status() {
    print_status "Current container status:"
    
    # Show running containers related to the project
    local running_containers
    running_containers=$(docker ps --filter "name=kitco" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "No containers running")
    
    if [[ "$running_containers" = "No containers running" ]]; then
        print_success "No Kitco Research AI containers are running"
    else
        echo "$running_containers"
    fi
    
    # Show volumes if they exist
    local volumes
    volumes=$(docker volume ls --filter "name=kitco" --format "table {{.Name}}" 2>/dev/null | tail -n +2 || echo "")
    
    if [[ -n "$volumes" ]]; then
        echo
        print_status "Remaining volumes:"
        echo "$volumes"
        echo
        print_status "Use '$0 -v' to remove volumes (WARNING: This deletes all data!)"
    fi
}

# Function to cleanup orphaned containers and networks
cleanup_orphans() {
    print_status "Cleaning up orphaned containers and networks..."
    
    # Remove orphaned containers
    docker compose down --remove-orphans &> /dev/null || true
    
    # Prune unused networks (only project-specific ones)
    docker network ls --filter "name=kitco" -q | xargs -r docker network rm &> /dev/null || true
    
    print_success "Cleanup completed"
}

# Main execution
main() {
    print_status "Stopping Kitco Research AI Docker deployment..."
    
    parse_args "$@"
    validate_environment
    check_prerequisites
    confirm_destructive_action
    stop_services
    remove_volumes
    remove_images
    cleanup_orphans
    show_status
    
    print_success "Docker stop completed!"
}

# Run main function with all arguments
main "$@"
