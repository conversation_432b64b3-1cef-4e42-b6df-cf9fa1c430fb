#!/bin/bash
# ============================================================================
# Kitco Research AI - Docker Container Startup Script
# ============================================================================
# Comprehensive script for starting the containerized application with SearXNG
# Supports both development and production environments
# ============================================================================

set -euo pipefail

# Script configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$PROJECT_ROOT/docker-compose.yml"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="dev"
PULL_IMAGES=false
BUILD_FRESH=false
SHOW_LOGS=false
DETACHED=true

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Start Kitco Research AI with SearXNG in Docker containers

OPTIONS:
    -e, --env ENVIRONMENT    Environment to run (dev|prod) [default: dev]
    -p, --pull              Pull latest images before starting
    -b, --build             Force rebuild of application images
    -l, --logs              Show logs after starting (implies --no-detach)
    -f, --foreground        Run in foreground (don't detach)
    -h, --help              Show this help message

EXAMPLES:
    $0                      # Start development environment
    $0 -e prod              # Start production environment
    $0 -e dev -b -l         # Rebuild and start dev with logs
    $0 -e prod -p           # Start production with latest images

ENVIRONMENT VARIABLES:
    SEARXNG_SECRET_KEY      Secret key for SearXNG (auto-generated if not set)
    OPENAI_API_KEY         OpenAI API key for LLM functionality

EOF
}

# Function to parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -p|--pull)
                PULL_IMAGES=true
                shift
                ;;
            -b|--build)
                BUILD_FRESH=true
                shift
                ;;
            -l|--logs)
                SHOW_LOGS=true
                DETACHED=false
                shift
                ;;
            -f|--foreground)
                DETACHED=false
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# Function to validate environment
validate_environment() {
    if [[ "$ENVIRONMENT" != "dev" && "$ENVIRONMENT" != "prod" ]]; then
        print_error "Invalid environment: $ENVIRONMENT. Must be 'dev' or 'prod'"
        exit 1
    fi
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is available
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    # Check if compose file exists
    if [[ ! -f "$COMPOSE_FILE" ]]; then
        print_error "Docker Compose file not found: $COMPOSE_FILE"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to setup environment variables
setup_environment() {
    print_status "Setting up environment variables..."
    
    # Create .env file if it doesn't exist
    ENV_FILE="$PROJECT_ROOT/.env"
    if [[ ! -f "$ENV_FILE" ]]; then
        print_warning ".env file not found. Creating with defaults..."
        cat > "$ENV_FILE" << EOF
# Kitco Research AI Environment Configuration
# Generated by docker-start.sh on $(date)

# Application Environment
ENVIRONMENT=$ENVIRONMENT
DEBUG=$([ "$ENVIRONMENT" = "dev" ] && echo "true" || echo "false")

# SearXNG Configuration
SEARXNG_SECRET_KEY=$(openssl rand -hex 32 2>/dev/null || echo "change-this-secret-key-in-production")

# Add your API keys here:
# OPENAI_API_KEY=your_openai_api_key_here
# SERPAPI_API_KEY=your_serpapi_key_here
EOF
        print_success "Created .env file with defaults"
    fi
    
    # Source the .env file
    if [[ -f "$ENV_FILE" ]]; then
        set -a
        source "$ENV_FILE"
        set +a
    fi
}

# Function to pull images if requested
pull_images() {
    if [[ "$PULL_IMAGES" = true ]]; then
        print_status "Pulling latest images..."
        docker compose --profile "$ENVIRONMENT" pull
        print_success "Images pulled successfully"
    fi
}

# Function to build images if requested
build_images() {
    if [[ "$BUILD_FRESH" = true ]]; then
        print_status "Building application images..."
        docker compose --profile "$ENVIRONMENT" build --no-cache
        print_success "Images built successfully"
    fi
}

# Function to start services
start_services() {
    print_status "Starting Kitco Research AI ($ENVIRONMENT environment)..."
    
    # Prepare docker compose command
    local compose_cmd="docker compose --profile $ENVIRONMENT"
    
    if [[ "$DETACHED" = true ]]; then
        compose_cmd="$compose_cmd up -d"
    else
        compose_cmd="$compose_cmd up"
    fi
    
    # Start the services
    eval "$compose_cmd"
    
    if [[ "$DETACHED" = true ]]; then
        print_success "Services started successfully in detached mode"
        
        # Wait a moment for services to initialize
        sleep 5
        
        # Show service status
        print_status "Service status:"
        docker compose --profile "$ENVIRONMENT" ps
        
        # Show access information
        echo
        print_success "Application is starting up!"
        echo -e "  ${GREEN}•${NC} Kitco Research AI: http://localhost:8765"
        echo -e "  ${GREEN}•${NC} SearXNG Search: http://localhost:8080"
        echo
        print_status "Use 'docker compose logs -f' to view logs"
        print_status "Use 'docker compose down' to stop all services"
    fi
}

# Function to show logs if requested
show_logs() {
    if [[ "$SHOW_LOGS" = true && "$DETACHED" = true ]]; then
        print_status "Showing logs (Ctrl+C to exit)..."
        docker compose --profile "$ENVIRONMENT" logs -f
    fi
}

# Main execution
main() {
    print_status "Starting Kitco Research AI Docker deployment..."
    
    parse_args "$@"
    validate_environment
    check_prerequisites
    setup_environment
    pull_images
    build_images
    start_services
    show_logs
    
    print_success "Docker startup completed!"
}

# Run main function with all arguments
main "$@"
