#!/usr/bin/env python3
"""
Provider Management CLI Utility
===============================

Command-line utility for managing LLM providers, configurations, and hot swapping.
Provides easy access to the unified provider system for maintenance and testing.

Usage:
    python scripts/manage_providers.py list
    python scripts/manage_providers.py switch openai
    python scripts/manage_providers.py models ollama
    python scripts/manage_providers.py health
    python scripts/manage_providers.py validate openai
"""

import sys
import argparse
from pathlib import Path
from typing import Dict, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.kitco_research_ai.config.provider_registry import get_provider_registry
    from src.kitco_research_ai.web.services.provider_service import get_provider_service
    from src.kitco_research_ai.utilities.db_utils import get_db_setting, get_settings_manager
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


def format_provider_info(provider_id: str, provider_config: Any) -> str:
    """Format provider information for display."""
    status = "✅ Available" if provider_config.available else "❌ Unavailable"
    type_icon = "☁️" if provider_config.type == "cloud" else "🏠"
    
    info = f"{type_icon} {provider_config.name} ({provider_id})\n"
    info += f"   Status: {status}\n"
    info += f"   Type: {provider_config.type.title()}\n"
    info += f"   Description: {provider_config.description}\n"
    
    if provider_config.error_message:
        info += f"   Error: {provider_config.error_message}\n"
    
    return info


def list_providers():
    """List all available providers."""
    print("🔍 Scanning for available providers...\n")
    
    try:
        registry = get_provider_registry()
        service = get_provider_service()
        
        # Get all providers (including disabled ones for admin view)
        all_providers = registry.get_all_providers(include_hidden=True, include_disabled=True)
        
        # Check health
        health_status = registry.check_provider_health(list(all_providers.keys()))
        
        # Get current selection
        current_provider = service.get_current_provider()
        current_model = service.get_current_model()
        
        print(f"📋 Found {len(all_providers)} providers:\n")
        
        # Sort by priority
        sorted_providers = sorted(all_providers.items(), key=lambda x: x[1].priority)
        
        for provider_id, provider_config in sorted_providers:
            # Update availability from health check
            provider_config.available = health_status.get(provider_id, False)
            
            # Mark current provider
            current_marker = " 👈 CURRENT" if provider_id == current_provider else ""
            
            print(f"{format_provider_info(provider_id, provider_config)}{current_marker}")
            print("-" * 50)
        
        print(f"\n🎯 Current Configuration:")
        print(f"   Provider: {current_provider}")
        print(f"   Model: {current_model}")
        
    except Exception as e:
        print(f"❌ Error listing providers: {e}")
        return False
    
    return True


def switch_provider(provider_id: str):
    """Switch to a different provider."""
    print(f"🔄 Switching to provider: {provider_id}")
    
    try:
        service = get_provider_service()
        success, message = service.set_current_provider(provider_id)
        
        if success:
            print(f"✅ {message}")
            
            # Show available models
            registry = get_provider_registry()
            models = registry.get_provider_models(provider_id)
            if models:
                print(f"\n📚 Available models for {provider_id}:")
                for model in models[:5]:  # Show first 5 models
                    print(f"   • {model}")
                if len(models) > 5:
                    print(f"   ... and {len(models) - 5} more")
        else:
            print(f"❌ {message}")
            return False
            
    except Exception as e:
        print(f"❌ Error switching provider: {e}")
        return False
    
    return True


def list_models(provider_id: str):
    """List available models for a provider."""
    print(f"📚 Getting models for provider: {provider_id}")
    
    try:
        registry = get_provider_registry()
        service = get_provider_service()
        
        # Check if provider exists
        provider = registry.get_provider(provider_id)
        if not provider:
            print(f"❌ Provider '{provider_id}' not found")
            return False
        
        # Get models (with refresh)
        success, models = service.refresh_provider_models(provider_id)
        
        if not success:
            print(f"❌ Failed to get models for {provider_id}")
            return False
        
        if not models:
            print(f"⚠️ No models found for {provider_id}")
            return True
        
        print(f"\n📋 Available models for {provider.name}:")
        for i, model in enumerate(models, 1):
            default_marker = " (default)" if model == provider.models.get('default') else ""
            print(f"   {i:2d}. {model}{default_marker}")
        
        print(f"\n📊 Total: {len(models)} models")
        
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return False
    
    return True


def check_health():
    """Check health of all providers."""
    print("🏥 Checking provider health...\n")
    
    try:
        service = get_provider_service()
        health_status = service.check_all_providers_health()
        
        print("📊 Health Status Report:")
        print("=" * 40)
        
        healthy_count = 0
        for provider_id, is_healthy in health_status.items():
            status = "✅ Healthy" if is_healthy else "❌ Unhealthy"
            print(f"{provider_id:15} {status}")
            if is_healthy:
                healthy_count += 1
        
        print("=" * 40)
        print(f"Summary: {healthy_count}/{len(health_status)} providers healthy")
        
    except Exception as e:
        print(f"❌ Error checking health: {e}")
        return False
    
    return True


def validate_provider(provider_id: str):
    """Validate a provider's configuration."""
    print(f"🔍 Validating provider: {provider_id}")
    
    try:
        service = get_provider_service()
        validation = service.validate_provider_configuration(provider_id)
        
        print(f"\n📋 Validation Results for {provider_id}:")
        print("=" * 40)
        
        if validation['valid']:
            print("✅ Configuration is valid")
        else:
            print("❌ Configuration has errors:")
            for error in validation['errors']:
                print(f"   • {error}")
        
        if validation['warnings']:
            print("\n⚠️ Warnings:")
            for warning in validation['warnings']:
                print(f"   • {warning}")
        
    except Exception as e:
        print(f"❌ Error validating provider: {e}")
        return False
    
    return True


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Manage LLM providers for Kitco Research AI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s list                    # List all providers
  %(prog)s switch openai           # Switch to OpenAI provider
  %(prog)s models ollama           # List models for Ollama
  %(prog)s health                  # Check all providers health
  %(prog)s validate openai         # Validate OpenAI configuration
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    subparsers.add_parser('list', help='List all available providers')
    
    # Switch command
    switch_parser = subparsers.add_parser('switch', help='Switch to a different provider')
    switch_parser.add_argument('provider', help='Provider ID to switch to')
    
    # Models command
    models_parser = subparsers.add_parser('models', help='List models for a provider')
    models_parser.add_argument('provider', help='Provider ID to list models for')
    
    # Health command
    subparsers.add_parser('health', help='Check health of all providers')
    
    # Validate command
    validate_parser = subparsers.add_parser('validate', help='Validate provider configuration')
    validate_parser.add_argument('provider', help='Provider ID to validate')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🚀 Kitco Research AI - Provider Management\n")
    
    # Execute command
    success = True
    if args.command == 'list':
        success = list_providers()
    elif args.command == 'switch':
        success = switch_provider(args.provider)
    elif args.command == 'models':
        success = list_models(args.provider)
    elif args.command == 'health':
        success = check_health()
    elif args.command == 'validate':
        success = validate_provider(args.provider)
    
    if not success:
        sys.exit(1)


if __name__ == '__main__':
    main()
