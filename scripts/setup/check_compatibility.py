#!/usr/bin/env python3
"""
Kitco Research AI - Python Version Compatibility Checker
This script checks package compatibility with the current Python version
"""

import sys
import subprocess
import json
from pathlib import Path
from typing import List, Dict, Tuple
import requests


def get_python_version() -> Tuple[int, int]:
    """Get current Python version as (major, minor)"""
    return sys.version_info.major, sys.version_info.minor


def get_project_root() -> Path:
    """Find the project root directory"""
    current = Path(__file__).parent
    while current.parent != current:
        if (current / 'app.py').exists() or (current / '.git').exists():
            return current
        current = current.parent
    return Path(__file__).parent.parent.parent


def parse_requirements_file(file_path: Path) -> List[str]:
    """Parse requirements file and extract package names"""
    packages = []
    if not file_path.exists():
        return packages
    
    with open(file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and not line.startswith('-r'):
                # Extract package name (before ==, >=, etc.)
                package = line.split('==')[0].split('>=')[0].split('<=')[0].split('~=')[0].split('!=')[0]
                packages.append(package.strip())
    
    return packages


def check_package_compatibility(package: str, python_version: Tuple[int, int]) -> Dict:
    """Check if a package supports the given Python version"""
    try:
        # Get package info from PyPI
        response = requests.get(f"https://pypi.org/pypi/{package}/json", timeout=10)
        if response.status_code != 200:
            return {"status": "unknown", "error": "Package not found on PyPI"}
        
        data = response.json()
        
        # Check classifiers for Python version support
        classifiers = data.get("info", {}).get("classifiers", [])
        python_classifiers = [c for c in classifiers if c.startswith("Programming Language :: Python ::")]
        
        # Check if current Python version is supported
        version_str = f"{python_version[0]}.{python_version[1]}"
        supported_versions = []
        
        for classifier in python_classifiers:
            if " :: " in classifier:
                version_part = classifier.split(" :: ")[-1]
                if version_part.replace(".", "").isdigit():
                    supported_versions.append(version_part)
        
        is_supported = version_str in supported_versions or not supported_versions
        
        return {
            "status": "supported" if is_supported else "unsupported",
            "supported_versions": supported_versions,
            "latest_version": data.get("info", {}).get("version", "unknown")
        }
        
    except Exception as e:
        return {"status": "error", "error": str(e)}


def main():
    """Main compatibility check function"""
    print("🔍 Kitco Research AI - Python Compatibility Checker")
    print("=" * 60)
    
    python_version = get_python_version()
    print(f"🐍 Python Version: {python_version[0]}.{python_version[1]}")
    print()
    
    project_root = get_project_root()
    requirements_files = [
        project_root / "config" / "requirements.txt",
        project_root / "config" / "requirements-dev.txt"
    ]
    
    all_packages = set()
    for req_file in requirements_files:
        if req_file.exists():
            packages = parse_requirements_file(req_file)
            all_packages.update(packages)
            print(f"📄 Found {len(packages)} packages in {req_file.name}")
    
    print(f"\n🔍 Checking {len(all_packages)} unique packages for Python {python_version[0]}.{python_version[1]} compatibility...")
    print()
    
    incompatible_packages = []
    unknown_packages = []
    error_packages = []
    
    for i, package in enumerate(sorted(all_packages), 1):
        print(f"[{i:3d}/{len(all_packages)}] Checking {package}...", end=" ")
        
        result = check_package_compatibility(package, python_version)
        
        if result["status"] == "supported":
            print("✅")
        elif result["status"] == "unsupported":
            print("❌")
            incompatible_packages.append((package, result))
        elif result["status"] == "unknown":
            print("❓")
            unknown_packages.append((package, result))
        else:
            print("⚠️")
            error_packages.append((package, result))
    
    print("\n" + "=" * 60)
    print("📊 COMPATIBILITY REPORT")
    print("=" * 60)
    
    if incompatible_packages:
        print(f"\n❌ INCOMPATIBLE PACKAGES ({len(incompatible_packages)}):")
        for package, result in incompatible_packages:
            supported = ", ".join(result.get("supported_versions", []))
            print(f"   • {package} (supports: {supported or 'unknown'})")
    
    if unknown_packages:
        print(f"\n❓ UNKNOWN COMPATIBILITY ({len(unknown_packages)}):")
        for package, result in unknown_packages:
            print(f"   • {package} ({result.get('error', 'No version info')})")
    
    if error_packages:
        print(f"\n⚠️ CHECK ERRORS ({len(error_packages)}):")
        for package, result in error_packages:
            print(f"   • {package} ({result.get('error', 'Unknown error')})")
    
    compatible_count = len(all_packages) - len(incompatible_packages) - len(unknown_packages) - len(error_packages)
    print(f"\n✅ COMPATIBLE PACKAGES: {compatible_count}")
    
    # Generate recommendations
    print("\n💡 RECOMMENDATIONS:")
    if incompatible_packages:
        print("   1. Create version-specific requirements file")
        print("   2. Find alternative packages or newer versions")
        print("   3. Consider using an older Python version")
    else:
        print("   ✅ All packages appear compatible!")
    
    print(f"\n📝 Version-specific file: config/requirements-python-{python_version[0]}.{python_version[1]}.txt")
    
    return len(incompatible_packages) == 0


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
