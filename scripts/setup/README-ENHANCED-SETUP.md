# Enhanced Setup System

This document describes the enhanced setup system for Kitco Research AI that prevents setup failures and ensures reliable installation across all platforms.

## Overview

The enhanced setup system includes:

1. **Automatic Dependency Validation** - Detects and fixes common dependency issues
2. **Cross-Platform Compatibility** - Works reliably on Windows, macOS, and Linux
3. **Conflict Resolution** - Automatically resolves version conflicts
4. **CI/CD Integration** - Prevents setup issues from reaching users
5. **Pre-commit Hooks** - Validates changes before they're committed

## Components

### Core Scripts

- `dependency_validator.py` - Validates and fixes dependency issues
- `setup_enhanced.py` - Enhanced setup with automatic validation
- `setup_cross_platform.py` - Cross-platform setup wrapper (enhanced)

### CI/CD Integration

- `.github/workflows/validate-setup.yml` - GitHub Actions workflow
- `scripts/ci/validate_setup.py` - Comprehensive CI validation
- `scripts/hooks/pre-commit-setup-validation.py` - Pre-commit hook

## Usage

### Basic Setup

```bash
# Use enhanced setup (recommended)
python scripts/setup/setup_enhanced.py setup --environment development

# Or use cross-platform wrapper
python scripts/setup/setup_cross_platform.py setup --development
```

### Dependency Validation

```bash
# Validate dependencies only
python scripts/setup/dependency_validator.py

# Fix dependency issues
python scripts/setup/setup_enhanced.py fix-deps

# Validate through cross-platform script
python scripts/setup/setup_cross_platform.py validate
```

### Environment Options

```bash
# Development environment (includes dev tools)
python scripts/setup/setup_enhanced.py setup --environment development

# Production environment (minimal dependencies)
python scripts/setup/setup_enhanced.py setup --environment production

# Standard environment (all features, no dev tools)
python scripts/setup/setup_enhanced.py setup --environment standard
```

## Features

### Automatic Dependency Fixes

The system automatically fixes common issues:

- **Package name corrections**: `vcr.py` → `vcrpy`, `pandas-profiling` → `ydata-profiling`
- **Version conflicts**: Downgrades incompatible versions automatically
- **Deprecated packages**: Comments out or removes deprecated dependencies
- **Missing templates**: Creates required template files

### Version Compatibility

Automatically resolves conflicts between:
- `safety` and `filelock`/`psutil`
- `ydata-profiling` and `matplotlib`/`numpy`
- `safety-schemas` and `pydantic`
- `unstructured-client` and `pydantic`

### Cross-Platform Support

- **Windows**: Uses `.bat` scripts and Windows-specific paths
- **macOS**: Uses shell scripts with macOS-specific configurations
- **Linux**: Uses shell scripts with Linux-specific configurations

### Backup and Recovery

- Creates automatic backups before making changes
- Provides detailed reports of all changes made
- Allows rollback if issues occur

## CI/CD Integration

### GitHub Actions

The workflow automatically:
- Validates dependencies on every PR
- Tests setup across multiple Python versions
- Tests on Windows, macOS, and Linux
- Creates issues when validation fails
- Runs security audits

### Pre-commit Hooks

Install the pre-commit hook:

```bash
# Copy the hook
cp scripts/hooks/pre-commit-setup-validation.py .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit

# Or use pre-commit framework
pip install pre-commit
pre-commit install
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Run dependency validation first
2. **Version Conflicts**: Use the enhanced setup which auto-fixes these
3. **Platform Issues**: Use the cross-platform script
4. **Permission Errors**: Ensure you have write access to the project directory

### Debug Mode

```bash
# Run with verbose output
python scripts/setup/setup_enhanced.py setup --environment development --verbose

# Check specific issues
python scripts/setup/dependency_validator.py --debug
```

### Manual Fixes

If automatic fixes don't work:

1. Check the validation report in `config/dependency_validation_report.txt`
2. Review backup files in `config/backups/`
3. Manually edit requirements files based on the report
4. Re-run validation

## Development

### Adding New Dependency Fixes

Edit `dependency_validator.py`:

```python
# Add to package_corrections
self.package_corrections = {
    "old-package-name": "new-package-name",
    "deprecated-package": None,  # Remove entirely
}

# Add to compatibility_constraints
self.compatibility_constraints = {
    "constraining-package": {
        "constrained-package": "version-spec",
    }
}
```

### Testing Changes

```bash
# Test locally
python scripts/ci/validate_setup.py

# Test specific environment
python scripts/setup/setup_enhanced.py setup --environment production --force
```

## Best Practices

1. **Always validate** before committing dependency changes
2. **Test across platforms** when modifying setup scripts
3. **Update constraints** when adding new packages
4. **Document changes** in the validation system
5. **Monitor CI/CD** for setup failures

## Migration from Old Setup

The enhanced setup is backward compatible:

1. Old scripts still work but show deprecation warnings
2. Enhanced scripts automatically fix issues the old scripts couldn't handle
3. Gradual migration is supported - you can use both systems

## Support

If you encounter setup issues:

1. Check the validation report
2. Review CI/CD logs
3. Run the enhanced setup with debug mode
4. Create an issue with the validation report attached

## Future Enhancements

Planned improvements:
- Support for more package managers (conda, poetry)
- Better version constraint solving
- Integration with dependency scanning tools
- Automatic security vulnerability fixes
