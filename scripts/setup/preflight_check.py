#!/usr/bin/env python3
"""
Kitco Research AI - Pre-flight Check Script
This script performs comprehensive system validation before setup
"""

import os
import sys
import platform
import subprocess
import shutil
import socket
from pathlib import Path


def print_header():
    """Print the header"""
    print("🔍 Kitco Research AI - Pre-flight Check")
    print("=" * 50)
    print()


def check_system_info():
    """Check and display system information"""
    print("🖥️  System Information:")
    print(f"   OS: {platform.system()} {platform.release()}")
    print(f"   Architecture: {platform.machine()}")
    print(f"   Python: {sys.version.split()[0]}")
    print(f"   Platform: {platform.platform()}")
    print()


def check_python_installation():
    """Check Python installation and version"""
    print("🐍 Python Installation Check:")
    
    # Check Python version
    version = sys.version_info
    if version >= (3, 10):
        print(f"   ✅ Python {version.major}.{version.minor}.{version.micro} (OK)")
    else:
        print(f"   ❌ Python {version.major}.{version.minor}.{version.micro} (Need 3.10+)")
        return False
    
    # Check pip
    try:
        import pip
        print(f"   ✅ pip available")
    except ImportError:
        print(f"   ❌ pip not available")
        return False
    
    # Check venv module
    try:
        import venv
        print(f"   ✅ venv module available")
    except ImportError:
        print(f"   ❌ venv module not available")
        return False
    
    print()
    return True


def check_network_connectivity():
    """Check network connectivity"""
    print("🌐 Network Connectivity Check:")
    
    test_hosts = [
        ("pypi.org", 443, "PyPI (Python packages)"),
        ("github.com", 443, "GitHub"),
        ("google.com", 443, "General internet"),
    ]
    
    all_good = True
    for host, port, description in test_hosts:
        try:
            sock = socket.create_connection((host, port), timeout=5)
            sock.close()
            print(f"   ✅ {description} ({host})")
        except (socket.error, socket.timeout):
            print(f"   ❌ {description} ({host}) - Connection failed")
            all_good = False
    
    print()
    return all_good


def check_disk_space():
    """Check available disk space"""
    print("💾 Disk Space Check:")
    
    try:
        if platform.system() == "Windows":
            import shutil
            total, used, free = shutil.disk_usage(".")
        else:
            statvfs = os.statvfs(".")
            total = statvfs.f_frsize * statvfs.f_blocks
            free = statvfs.f_frsize * statvfs.f_available
            used = total - free
        
        total_gb = total / (1024**3)
        free_gb = free / (1024**3)
        used_gb = used / (1024**3)
        
        print(f"   📊 Total: {total_gb:.1f} GB")
        print(f"   📊 Used: {used_gb:.1f} GB")
        print(f"   📊 Free: {free_gb:.1f} GB")
        
        # Check if we have enough space (need at least 2GB free)
        if free_gb >= 2.0:
            print(f"   ✅ Sufficient disk space")
            result = True
        else:
            print(f"   ❌ Insufficient disk space (need at least 2GB)")
            result = False
            
    except Exception as e:
        print(f"   ❌ Could not check disk space: {e}")
        result = False
    
    print()
    return result


def check_required_commands():
    """Check for required system commands"""
    print("🔧 Required Commands Check:")
    
    commands = []
    if platform.system() == "Windows":
        commands = [
            ("python", "Python interpreter"),
            ("pip", "Python package installer"),
            ("git", "Version control (optional)"),
        ]
    else:
        commands = [
            ("python3", "Python interpreter"),
            ("pip3", "Python package installer"),
            ("git", "Version control (optional)"),
            ("curl", "HTTP client"),
            ("wget", "File downloader (optional)"),
        ]
    
    all_good = True
    for cmd, description in commands:
        if shutil.which(cmd):
            print(f"   ✅ {cmd} - {description}")
        else:
            if cmd in ["git", "wget"]:
                print(f"   ⚠️ {cmd} - {description} (optional)")
            else:
                print(f"   ❌ {cmd} - {description} (required)")
                all_good = False
    
    print()
    return all_good


def check_ports():
    """Check if required ports are available"""
    print("🔌 Port Availability Check:")
    
    ports_to_check = [8765, 5000, 5001, 5002, 8080]
    
    all_good = True
    for port in ports_to_check:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"   ⚠️ Port {port} is in use")
                if port == 8765:
                    all_good = False
            else:
                print(f"   ✅ Port {port} is available")
                
        except Exception as e:
            print(f"   ⚠️ Could not check port {port}: {e}")
    
    print()
    return all_good


def check_permissions():
    """Check file system permissions"""
    print("🔐 Permissions Check:")
    
    try:
        # Test write permission in current directory
        test_file = Path("test_write_permission.tmp")
        test_file.write_text("test")
        test_file.unlink()
        print("   ✅ Write permission in current directory")
        
        # Test if we can create directories
        test_dir = Path("test_dir_permission")
        test_dir.mkdir(exist_ok=True)
        test_dir.rmdir()
        print("   ✅ Directory creation permission")
        
        result = True
        
    except PermissionError:
        print("   ❌ Insufficient permissions")
        print("   💡 Try running with elevated privileges")
        result = False
    except Exception as e:
        print(f"   ❌ Permission check failed: {e}")
        result = False
    
    print()
    return result


def check_project_structure():
    """Check if we're in the right project directory"""
    print("📁 Project Structure Check:")
    
    required_files = [
        "app.py",
        "src/kitco_research_ai",
        "config/requirements.txt",
        "scripts",
    ]
    
    all_good = True
    for item in required_files:
        path = Path(item)
        if path.exists():
            print(f"   ✅ {item}")
        else:
            print(f"   ❌ {item} (missing)")
            all_good = False
    
    if not all_good:
        print("   💡 Make sure you're in the kitco_research_ai project root directory")
    
    print()
    return all_good


def check_antivirus_interference():
    """Check for potential antivirus interference"""
    print("🛡️  Antivirus/Security Check:")
    
    if platform.system() == "Windows":
        print("   ⚠️ On Windows, antivirus software may interfere with:")
        print("      • Python package installation")
        print("      • Virtual environment creation")
        print("      • File downloads")
        print("   💡 Consider temporarily disabling real-time protection")
        print("      or adding project directory to exclusions")
    else:
        print("   ✅ Unix-like systems typically have fewer interference issues")
    
    print()
    return True


def provide_recommendations(results):
    """Provide recommendations based on check results"""
    print("💡 Recommendations:")
    print("=" * 20)
    
    if not results['python']:
        print("🐍 Python Issues:")
        print("   • Install Python 3.10+ from https://python.org")
        print("   • Ensure Python is added to PATH")
        print("   • On Ubuntu/Debian: sudo apt install python3.10 python3.10-venv python3-pip")
        print("   • On macOS: brew install python@3.10")
        print()
    
    if not results['network']:
        print("🌐 Network Issues:")
        print("   • Check internet connection")
        print("   • Verify firewall settings")
        print("   • Check proxy configuration")
        print()
    
    if not results['disk_space']:
        print("💾 Disk Space Issues:")
        print("   • Free up at least 2GB of disk space")
        print("   • Consider moving to a drive with more space")
        print()
    
    if not results['commands']:
        print("🔧 Missing Commands:")
        print("   • Install missing required commands")
        print("   • Update PATH environment variable")
        print()
    
    if not results['ports']:
        print("🔌 Port Issues:")
        print("   • Stop services using port 8765")
        print("   • Use scripts/restart.sh to clean up")
        print()
    
    if not results['permissions']:
        print("🔐 Permission Issues:")
        print("   • Run with administrator/sudo privileges")
        print("   • Check directory ownership")
        print()
    
    if not results['project']:
        print("📁 Project Structure Issues:")
        print("   • Navigate to the correct project directory")
        print("   • Ensure all project files are present")
        print()


def main():
    """Main pre-flight check function"""
    print_header()
    
    # Run all checks
    results = {
        'system': check_system_info(),
        'python': check_python_installation(),
        'network': check_network_connectivity(),
        'disk_space': check_disk_space(),
        'commands': check_required_commands(),
        'ports': check_ports(),
        'permissions': check_permissions(),
        'project': check_project_structure(),
        'antivirus': check_antivirus_interference(),
    }
    
    # Summary
    print("📋 Pre-flight Check Summary:")
    print("=" * 30)
    
    passed = 0
    total = len([k for k in results.keys() if k != 'system'])
    
    for check, result in results.items():
        if check == 'system':
            continue
        if result:
            print(f"   ✅ {check.replace('_', ' ').title()}")
            passed += 1
        else:
            print(f"   ❌ {check.replace('_', ' ').title()}")
    
    print()
    print(f"📊 Score: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! You're ready to run setup.")
        print("🚀 Run: python scripts/validate_requirements.py")
        return 0
    else:
        print("⚠️ Some checks failed. Please address the issues below.")
        provide_recommendations(results)
        return 1


if __name__ == '__main__':
    sys.exit(main())
