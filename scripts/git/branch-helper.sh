#!/bin/bash

# ============================================================================
# Git Branch Helper Script
# ============================================================================
# Helps developers create and manage branches according to our strategy
# ============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_success() { echo -e "${GREEN}✅ $1${NC}"; }
print_error() { echo -e "${RED}❌ $1${NC}"; }
print_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
print_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }

# Show usage
show_usage() {
    echo "🚀 Git Branch Helper"
    echo ""
    echo "Usage: $0 <command> [options]"
    echo ""
    echo "Commands:"
    echo "  feature <name>     Create a new feature branch"
    echo "  release <version>  Create a new release branch"
    echo "  hotfix <name>      Create a new hotfix branch"
    echo "  cleanup            Clean up merged branches"
    echo "  status             Show current branch status"
    echo "  help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 feature user-authentication"
    echo "  $0 release v1.2.0"
    echo "  $0 hotfix security-patch"
    echo "  $0 cleanup"
}

# Check if we're in a git repository
check_git_repo() {
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        print_error "Not in a git repository!"
        exit 1
    fi
}

# Get current branch
get_current_branch() {
    git branch --show-current
}

# Check if branch exists
branch_exists() {
    git show-ref --verify --quiet refs/heads/"$1"
}

# Create feature branch
create_feature_branch() {
    local feature_name="$1"
    
    if [[ -z "$feature_name" ]]; then
        print_error "Feature name is required!"
        echo "Usage: $0 feature <feature-name>"
        exit 1
    fi
    
    local branch_name="feature/$feature_name"
    
    print_info "Creating feature branch: $branch_name"
    
    # Check if branch already exists
    if branch_exists "$branch_name"; then
        print_error "Branch '$branch_name' already exists!"
        exit 1
    fi
    
    # Switch to develop and pull latest
    print_info "Switching to develop branch..."
    git checkout develop
    
    print_info "Pulling latest changes..."
    git pull origin develop
    
    # Create and switch to feature branch
    print_info "Creating feature branch..."
    git checkout -b "$branch_name"
    
    print_success "Feature branch '$branch_name' created successfully!"
    print_info "You can now start working on your feature."
    print_info "When ready, push with: git push origin $branch_name"
}

# Create release branch
create_release_branch() {
    local version="$1"
    
    if [[ -z "$version" ]]; then
        print_error "Version is required!"
        echo "Usage: $0 release <version>"
        echo "Example: $0 release v1.2.0"
        exit 1
    fi
    
    local branch_name="release/$version"
    
    print_info "Creating release branch: $branch_name"
    
    # Check if branch already exists
    if branch_exists "$branch_name"; then
        print_error "Branch '$branch_name' already exists!"
        exit 1
    fi
    
    # Switch to develop and pull latest
    print_info "Switching to develop branch..."
    git checkout develop
    
    print_info "Pulling latest changes..."
    git pull origin develop
    
    # Create and switch to release branch
    print_info "Creating release branch..."
    git checkout -b "$branch_name"
    
    print_success "Release branch '$branch_name' created successfully!"
    print_info "Now you can:"
    print_info "1. Update version numbers"
    print_info "2. Fix any last-minute bugs"
    print_info "3. Update documentation"
    print_info "4. Push with: git push origin $branch_name"
    print_info "5. Create PR to main when ready"
}

# Create hotfix branch
create_hotfix_branch() {
    local hotfix_name="$1"
    
    if [[ -z "$hotfix_name" ]]; then
        print_error "Hotfix name is required!"
        echo "Usage: $0 hotfix <hotfix-name>"
        exit 1
    fi
    
    local branch_name="hotfix/$hotfix_name"
    
    print_warning "Creating HOTFIX branch: $branch_name"
    print_warning "This is for EMERGENCY fixes only!"
    
    # Check if branch already exists
    if branch_exists "$branch_name"; then
        print_error "Branch '$branch_name' already exists!"
        exit 1
    fi
    
    # Switch to main and pull latest
    print_info "Switching to main branch..."
    git checkout main
    
    print_info "Pulling latest changes..."
    git pull origin main
    
    # Create and switch to hotfix branch
    print_info "Creating hotfix branch..."
    git checkout -b "$branch_name"
    
    print_success "Hotfix branch '$branch_name' created successfully!"
    print_warning "Remember:"
    print_warning "1. Keep changes minimal and focused"
    print_warning "2. Test thoroughly"
    print_warning "3. Create PR to BOTH main AND develop"
}

# Clean up merged branches
cleanup_branches() {
    print_info "Cleaning up merged branches..."
    
    # Switch to develop
    git checkout develop
    git pull origin develop
    
    # Get list of merged branches (excluding main, develop, and current branch)
    local merged_branches
    merged_branches=$(git branch --merged | grep -v -E "(main|develop|\*)" | xargs -n 1)
    
    if [[ -z "$merged_branches" ]]; then
        print_info "No merged branches to clean up."
        return
    fi
    
    print_info "Found merged branches:"
    echo "$merged_branches"
    
    read -p "Delete these branches? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "$merged_branches" | xargs -n 1 git branch -d
        print_success "Merged branches cleaned up!"
    else
        print_info "Cleanup cancelled."
    fi
}

# Show branch status
show_status() {
    print_info "Current branch status:"
    echo ""
    
    local current_branch
    current_branch=$(get_current_branch)
    
    echo "📍 Current branch: $current_branch"
    echo ""
    
    print_info "All branches:"
    git branch -a
    echo ""
    
    print_info "Recent commits:"
    git log --oneline -5
    echo ""
    
    print_info "Working directory status:"
    git status --short
}

# Main function
main() {
    check_git_repo
    
    case "${1:-}" in
        "feature")
            create_feature_branch "$2"
            ;;
        "release")
            create_release_branch "$2"
            ;;
        "hotfix")
            create_hotfix_branch "$2"
            ;;
        "cleanup")
            cleanup_branches
            ;;
        "status")
            show_status
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        "")
            print_error "No command specified!"
            echo ""
            show_usage
            exit 1
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
