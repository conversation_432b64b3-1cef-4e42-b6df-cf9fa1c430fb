@echo off
:: Kitco Research AI - Fresh Restart Script for Windows
:: This script performs a clean restart of the application

setlocal enabledelayedexpansion

echo 🔄 Kitco Research AI - Fresh Restart
echo ====================================

:: Find project root directory
call :find_project_root
cd /d "%PROJECT_ROOT%"

echo 📁 Project Root: %PROJECT_ROOT%
echo.

:: Function to kill processes on port
call :kill_port_processes 8765
call :kill_port_processes 5000
call :kill_port_processes 5001
call :kill_port_processes 5002
call :kill_port_processes 8080
echo.

:: Check virtual environment
echo 🐍 Checking virtual environment...
if not exist ".venv" (
    echo ❌ Virtual environment not found!
    echo 🔧 Please run the initial setup first:
    echo    scripts\setup.bat
    exit /b 1
)
echo ✅ Virtual environment found
echo.

:: Activate virtual environment
echo 🔄 Activating virtual environment...
call .venv\Scripts\activate.bat
echo ✅ Virtual environment activated
echo.

:: Detect environment and choose appropriate requirements file
echo 🔍 Detecting environment...
set REQUIREMENTS_FILE=config\requirements.txt
set ENV_NAME=Standard

:: Check if development tools are available to determine environment
python -c "import pytest, black, mypy" 2>nul
if not errorlevel 1 (
    set REQUIREMENTS_FILE=config\requirements-dev.txt
    set ENV_NAME=Development
    echo 📚 Development environment detected
) else (
    python -c "import pytest" 2>nul
    if errorlevel 1 (
        if exist "config\requirements-prod.txt" (
            set REQUIREMENTS_FILE=config\requirements-prod.txt
            set ENV_NAME=Production
            echo 🏭 Production environment detected
        )
    )
)

echo 🎯 Using %ENV_NAME% environment (%REQUIREMENTS_FILE%)
echo.

:: Check if dependencies are installed
echo 📦 Checking dependencies...
python -c "import flask, langchain, playwright" 2>nul
if errorlevel 1 (
    echo ⚠️ Some dependencies missing, reinstalling...
    echo 📄 Installing from: %REQUIREMENTS_FILE%
    pip install -r "%REQUIREMENTS_FILE%"
    echo ✅ Dependencies reinstalled
) else (
    echo ✅ Core dependencies check passed

    :: Additional check for development tools if in dev environment
    if "%ENV_NAME%"=="Development" (
        python -c "import pytest, black, mypy" 2>nul
        if errorlevel 1 (
            echo ⚠️ Development tools missing, reinstalling...
            pip install -r "%REQUIREMENTS_FILE%"
            echo ✅ Development dependencies reinstalled
        ) else (
            echo ✅ Development tools check passed
        )
    )
)
echo.

:: Check Playwright browsers
echo 🌐 Checking Playwright browsers...
python -c "from playwright.sync_api import sync_playwright; sync_playwright().start().chromium.launch()" 2>nul
if errorlevel 1 (
    echo ⚠️ Playwright browsers missing, reinstalling...
    python -m playwright install
    echo ✅ Playwright browsers reinstalled
) else (
    echo ✅ Playwright browsers available
)
echo.

:: Clean up any temporary files
echo 🧹 Cleaning up temporary files...
for /r . %%f in (*.pyc) do del "%%f" 2>nul
for /d /r . %%d in (__pycache__) do rd /s /q "%%d" 2>nul
for /d /r . %%d in (.pytest_cache) do rd /s /q "%%d" 2>nul
echo ✅ Cleanup completed
echo.

:: Check database
echo 🗄️ Checking database...
if exist "data\ldr.db" (
    echo ✅ Database found
) else (
    echo ⚠️ Database not found, initializing...
    python src\kitco_research_ai\setup_data_dir.py
    echo ✅ Database initialized
)
echo.

:: Get port preference
if "%~1"=="" (
    set PORT=8765
) else (
    set PORT=%~1
)

echo 🎉 Fresh Restart Complete!
echo ==========================
echo.
echo ✅ Environment: Ready
echo ✅ Dependencies: Verified
echo ✅ Browsers: Available
echo ✅ Database: Ready
echo ✅ Cleanup: Completed
echo.
echo 🚀 Starting application on port %PORT%...
echo 🌐 Access at: http://localhost:%PORT%
echo ⏹️ Press Ctrl+C to stop
echo.

:: Start the application
python app.py --port=%PORT%

exit /b 0

:: Function to find project root directory
:find_project_root
set "CURRENT_DIR=%CD%"

:: Check if we're already in project root (has app.py and src/ directory)
if exist "app.py" if exist "src\kitco_research_ai" (
    set "PROJECT_ROOT=%CD%"
    exit /b 0
)

:: Search upward for project root
set "SEARCH_DIR=%CURRENT_DIR%"
:search_loop
if "%SEARCH_DIR%"=="" goto search_failed
if exist "%SEARCH_DIR%\app.py" if exist "%SEARCH_DIR%\src\kitco_research_ai" (
    set "PROJECT_ROOT=%SEARCH_DIR%"
    exit /b 0
)
for %%I in ("%SEARCH_DIR%\.") do set "PARENT_DIR=%%~dpI"
set "PARENT_DIR=%PARENT_DIR:~0,-1%"
if "%PARENT_DIR%"=="%SEARCH_DIR%" goto search_failed
set "SEARCH_DIR=%PARENT_DIR%"
goto search_loop

:search_failed
echo ❌ Error: Could not find project root directory
echo    Please run this script from the kitco_research_ai project directory
exit /b 1

:: Function to kill processes on port
:kill_port_processes
set PORT_TO_KILL=%~1
echo 🔍 Checking for processes on port %PORT_TO_KILL%...

:: Find processes using the port
for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":%PORT_TO_KILL% "') do (
    set PID=%%a
    if defined PID (
        echo ⚠️ Found process on port %PORT_TO_KILL%: !PID!
        echo 🛑 Stopping process...
        taskkill /PID !PID! /F >nul 2>&1
        echo ✅ Port %PORT_TO_KILL% cleared
        goto :eof
    )
)
echo ✅ Port %PORT_TO_KILL% is free
exit /b 0
