#!/usr/bin/env python3
"""
Security Check Script
====================

Scans the repository for potential sensitive data that might be accidentally
committed to version control. This script helps ensure no API keys, passwords,
or other sensitive information is present in the codebase.
"""

import os
import re
import sys
from pathlib import Path
from typing import List, Dict, Tuple

class SecurityScanner:
    """Security scanner for detecting sensitive data in files"""
    
    def __init__(self):
        self.root_dir = Path(__file__).parent.parent
        self.issues = []
        self.scanned_files = 0
        
        # Patterns for sensitive data detection
        self.sensitive_patterns = {
            'api_keys': [
                r'(?i)(api[_-]?key|apikey)\s*[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
                r'(?i)(secret[_-]?key|secretkey)\s*[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
                r'(?i)(access[_-]?token|accesstoken)\s*[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
                r'(?i)(bearer[_-]?token|bearertoken)\s*[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
            ],
            'openai_keys': [
                r'sk-[a-zA-Z0-9]{48}',  # OpenAI API key pattern
                r'(?i)openai[_-]?api[_-]?key\s*[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
                r'(?i)custom[_-]?endpoint\s*[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',  # Custom endpoint API key pattern
            ],
            'anthropic_keys': [
                r'(?i)anthropic[_-]?api[_-]?key\s*[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
                r'(?i)claude[_-]?api[_-]?key\s*[:=]\s*["\']?([a-zA-Z0-9_-]{20,})["\']?',
            ],
            'passwords': [
                r'(?i)password\s*[:=]\s*["\']?([^"\'\s]{8,})["\']?',
                r'(?i)passwd\s*[:=]\s*["\']?([^"\'\s]{8,})["\']?',
                r'(?i)pwd\s*[:=]\s*["\']?([^"\'\s]{8,})["\']?',
            ],
            'database_urls': [
                r'(?i)(database[_-]?url|db[_-]?url)\s*[:=]\s*["\']?([^"\'\s]+://[^"\'\s]+)["\']?',
                r'(?i)connection[_-]?string\s*[:=]\s*["\']?([^"\'\s]+://[^"\'\s]+)["\']?',
            ],
            'private_keys': [
                r'-----BEGIN\s+(RSA\s+)?PRIVATE\s+KEY-----',
                r'-----BEGIN\s+OPENSSH\s+PRIVATE\s+KEY-----',
                r'-----BEGIN\s+EC\s+PRIVATE\s+KEY-----',
            ],
            'jwt_tokens': [
                r'eyJ[a-zA-Z0-9_-]+\.eyJ[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+',  # JWT pattern
            ],
            'aws_keys': [
                r'AKIA[0-9A-Z]{16}',  # AWS Access Key ID
                r'(?i)aws[_-]?access[_-]?key[_-]?id\s*[:=]\s*["\']?([A-Z0-9]{20})["\']?',
                r'(?i)aws[_-]?secret[_-]?access[_-]?key\s*[:=]\s*["\']?([a-zA-Z0-9/+=]{40})["\']?',
            ],
            'google_keys': [
                r'(?i)google[_-]?api[_-]?key\s*[:=]\s*["\']?([a-zA-Z0-9_-]{39})["\']?',
                r'(?i)gcp[_-]?api[_-]?key\s*[:=]\s*["\']?([a-zA-Z0-9_-]{39})["\']?',
            ],
        }
        
        # Files to skip (binary, large, or known safe files)
        self.skip_patterns = [
            r'\.git/',
            r'__pycache__/',
            r'\.pyc$',
            r'\.pyo$',
            r'\.so$',
            r'\.dylib$',
            r'\.dll$',
            r'\.exe$',
            r'\.bin$',
            r'\.db$',
            r'\.sqlite$',
            r'\.sqlite3$',
            r'\.log$',
            r'\.png$',
            r'\.jpg$',
            r'\.jpeg$',
            r'\.gif$',
            r'\.pdf$',
            r'\.zip$',
            r'\.tar\.gz$',
            r'\.tgz$',
            r'node_modules/',
            r'venv/',
            r'\.venv/',
            r'env/',
            r'\.env/',
        ]
    
    def should_skip_file(self, file_path: str) -> bool:
        """Check if file should be skipped"""
        for pattern in self.skip_patterns:
            if re.search(pattern, file_path):
                return True
        return False
    
    def scan_file(self, file_path: Path) -> List[Dict]:
        """Scan a single file for sensitive data"""
        if self.should_skip_file(str(file_path)):
            return []

        issues = []
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()

            for category, patterns in self.sensitive_patterns.items():
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.MULTILINE)
                    for match in matches:
                        # Get line number
                        line_num = content[:match.start()].count('\n') + 1

                        # Get the matched text (truncate if too long)
                        matched_text = match.group(0)
                        if len(matched_text) > 100:
                            matched_text = matched_text[:100] + "..."

                        # Skip false positives
                        if self._is_false_positive(matched_text, str(file_path), category):
                            continue

                        issues.append({
                            'file': str(file_path.relative_to(self.root_dir)),
                            'line': line_num,
                            'category': category,
                            'pattern': pattern,
                            'match': matched_text,
                            'severity': self._get_severity(category)
                        })

        except Exception as e:
            print(f"Warning: Could not scan {file_path}: {e}")

        return issues

    def _is_false_positive(self, matched_text: str, file_path: str, category: str) -> bool:
        """Check if a match is likely a false positive"""
        # Skip documentation examples with masked keys
        if 'docs/' in file_path and ('xxxxxxxx' in matched_text or 'your-' in matched_text):
            return True

        # Skip function parameter definitions
        if category == 'passwords' and ('password: Optional' in matched_text or 'password:' in matched_text):
            return True

        # Skip commented out examples
        if matched_text.strip().startswith('#'):
            return True

        # Skip template/example values
        template_indicators = ['your-', 'example-', 'sample-', 'test-', 'demo-', 'placeholder-']
        if any(indicator in matched_text.lower() for indicator in template_indicators):
            return True

        # Skip masked values (all x's or similar patterns)
        clean_text = matched_text.replace('=', '').replace(':', '').replace(' ', '').replace('_', '').replace('-', '')
        if (re.match(r'^[xX]+$', clean_text) or
            'xxxxxxxx' in matched_text or
            'XXXXXXXX' in matched_text or
            re.search(r'X{8,}', matched_text)):
            return True

        return False
    
    def _get_severity(self, category: str) -> str:
        """Get severity level for a category"""
        high_severity = ['api_keys', 'openai_keys', 'anthropic_keys', 'private_keys', 'aws_keys']
        medium_severity = ['passwords', 'database_urls', 'jwt_tokens', 'google_keys']
        
        if category in high_severity:
            return 'HIGH'
        elif category in medium_severity:
            return 'MEDIUM'
        else:
            return 'LOW'
    
    def scan_repository(self) -> None:
        """Scan the entire repository for sensitive data"""
        print("🔍 Scanning repository for sensitive data...")
        print(f"Root directory: {self.root_dir}")
        print()
        
        # Scan all files
        for file_path in self.root_dir.rglob("*"):
            if file_path.is_file():
                self.scanned_files += 1
                file_issues = self.scan_file(file_path)
                self.issues.extend(file_issues)
        
        print(f"📊 Scanned {self.scanned_files} files")
        print(f"🚨 Found {len(self.issues)} potential security issues")
    
    def print_results(self) -> None:
        """Print scan results"""
        if not self.issues:
            print("\n✅ No sensitive data detected!")
            print("🔒 Repository appears to be secure.")
            return
        
        # Group issues by severity
        high_issues = [i for i in self.issues if i['severity'] == 'HIGH']
        medium_issues = [i for i in self.issues if i['severity'] == 'MEDIUM']
        low_issues = [i for i in self.issues if i['severity'] == 'LOW']
        
        print(f"\n🚨 SECURITY ISSUES FOUND:")
        print(f"   HIGH: {len(high_issues)}")
        print(f"   MEDIUM: {len(medium_issues)}")
        print(f"   LOW: {len(low_issues)}")
        
        # Print high severity issues first
        if high_issues:
            print(f"\n🔥 HIGH SEVERITY ISSUES ({len(high_issues)}):")
            for issue in high_issues:
                print(f"   📁 {issue['file']}:{issue['line']}")
                print(f"      Category: {issue['category']}")
                print(f"      Match: {issue['match']}")
                print()
        
        # Print medium severity issues
        if medium_issues:
            print(f"\n⚠️  MEDIUM SEVERITY ISSUES ({len(medium_issues)}):")
            for issue in medium_issues:
                print(f"   📁 {issue['file']}:{issue['line']}")
                print(f"      Category: {issue['category']}")
                print(f"      Match: {issue['match']}")
                print()
        
        # Print low severity issues (summary only)
        if low_issues:
            print(f"\nℹ️  LOW SEVERITY ISSUES ({len(low_issues)}):")
            for issue in low_issues:
                print(f"   📁 {issue['file']}:{issue['line']} - {issue['category']}")
    
    def check_gitignore_coverage(self) -> None:
        """Check if .gitignore covers common sensitive file patterns"""
        print(f"\n🛡️  Checking .gitignore coverage...")
        
        gitignore_path = self.root_dir / ".gitignore"
        if not gitignore_path.exists():
            print("❌ No .gitignore file found!")
            return
        
        with open(gitignore_path, 'r') as f:
            gitignore_content = f.read()
        
        # Check for important patterns
        important_patterns = [
            '.env',
            '*.key',
            '*.pem',
            '*api_key*',
            '*secret*',
            '*.log',
            '__pycache__',
            '*.db',
        ]
        
        missing_patterns = []
        for pattern in important_patterns:
            if pattern not in gitignore_content:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print(f"⚠️  Missing .gitignore patterns: {missing_patterns}")
        else:
            print("✅ .gitignore has good coverage for sensitive files")


def main():
    """Main security check function"""
    print("=" * 70)
    print("🔒 SECURITY SCAN - KITCO RESEARCH AI")
    print("=" * 70)
    
    scanner = SecurityScanner()
    scanner.scan_repository()
    scanner.print_results()
    scanner.check_gitignore_coverage()
    
    print("\n" + "=" * 70)
    print("🔒 SECURITY SCAN COMPLETE")
    print("=" * 70)
    
    # Return exit code based on findings
    high_severity_count = len([i for i in scanner.issues if i['severity'] == 'HIGH'])
    if high_severity_count > 0:
        print(f"⚠️  Found {high_severity_count} high-severity issues!")
        print("🚨 REVIEW AND FIX BEFORE COMMITTING!")
        return 1
    else:
        print("✅ No high-severity security issues found.")
        return 0


if __name__ == "__main__":
    sys.exit(main())
