#!/bin/bash

# SearXNG Health Check Script
# This script provides a more robust health check for SearXNG

set -e

# Configuration
SEARXNG_URL="http://127.0.0.1:8080"
MAX_RETRIES=3
RETRY_DELAY=2

# Function to check if <PERSON><PERSON><PERSON><PERSON> is responding
check_searxng() {
    local attempt=1
    
    while [ $attempt -le $MAX_RETRIES ]; do
        echo "Health check attempt $attempt/$MAX_RETRIES..."
        
        # Check if SearXNG responds with a 200 status
        if curl -f -s --connect-timeout 5 --max-time 10 "$SEARXNG_URL/" > /dev/null 2>&1; then
            echo "✅ SearXNG is healthy"
            return 0
        fi
        
        echo "❌ SearXNG not ready, waiting ${RETRY_DELAY}s..."
        sleep $RETRY_DELAY
        attempt=$((attempt + 1))
    done
    
    echo "❌ SearXNG health check failed after $MAX_RETRIES attempts"
    return 1
}

# Run the health check
check_searxng
