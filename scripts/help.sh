#!/bin/bash

# Kitco Research AI - Help & Quick Reference
# This script shows available commands and usage information

# Function to find project root directory
find_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Check if we're already in project root (has app.py and src/ directory)
    if [ -f "app.py" ] && [ -d "src/kitco_research_ai" ]; then
        echo "$(pwd)"
        return 0
    fi

    # Check if script is in scripts/ subdirectory of project root
    local parent_dir="$(dirname "$script_dir")"
    if [ -f "$parent_dir/app.py" ] && [ -d "$parent_dir/src/kitco_research_ai" ]; then
        echo "$parent_dir"
        return 0
    fi

    # Search upward for project root
    local search_dir="$current_dir"
    while [ "$search_dir" != "/" ]; do
        if [ -f "$search_dir/app.py" ] && [ -d "$search_dir/src/kitco_research_ai" ]; then
            echo "$search_dir"
            return 0
        fi
        search_dir="$(dirname "$search_dir")"
    done

    # For help script, we can be more lenient and just show help even if not in project root
    echo "$(pwd)"
    return 0
}

# Change to project root directory (if possible)
PROJECT_ROOT="$(find_project_root)"
if [ -f "$PROJECT_ROOT/app.py" ]; then
    cd "$PROJECT_ROOT"
fi

echo "🔬 Kitco Research AI - Quick Reference"
echo "======================================"
echo "📁 Project Root: $PROJECT_ROOT"
echo ""

echo "📋 Available Scripts:"
echo ""

echo "🔧 INITIAL SETUP (run once):"
echo "   ./scripts/setup.sh [--env ENV]"
echo "   └── Development setup:  ./scripts/setup.sh --development"
echo "   └── Production setup:   ./scripts/setup.sh --production"
echo "   └── Standard setup:     ./scripts/setup.sh"
echo "   └── Help:               ./scripts/setup.sh --help"
echo ""

echo "🚀 START APPLICATION:"
echo "   ./scripts/start_app.sh [port]"
echo "   └── Start on default port 8765: ./scripts/start_app.sh"
echo "   └── Start on custom port:     ./scripts/start_app.sh 8080"
echo ""

echo "🔄 FRESH RESTART:"
echo "   ./scripts/restart.sh [port]"
echo "   └── Clean restart with checks: ./scripts/restart.sh"
echo "   └── Restart on custom port:   ./scripts/restart.sh 8080"
echo ""

echo "📊 STATUS CHECK:"
echo "   ./scripts/status.sh"
echo "   └── Check project setup and running status"
echo ""

echo "❓ HELP & REFERENCE:"
echo "   ./scripts/help.sh"
echo "   └── Show this help message"
echo ""

echo "🔄 REQUIREMENTS UPDATE:"
echo "   ./scripts/update_requirements.sh"
echo "   └── Migrate to new requirements system"
echo ""

echo "🐳 DOCKER COMMANDS:"
echo "   docker-compose --profile dev up     # Development"
echo "   docker-compose --profile prod up    # Production"
echo "   docker build -f Dockerfile .        # Custom build"
echo ""

echo "📁 Project Structure:"
echo "   .venv/                    - Virtual environment"
echo "   src/                      - Source code"
echo "   data/                     - Database and application data"
echo "   config/                   - Configuration files"
echo "   ├── requirements.txt      - Main requirements (comprehensive)"
echo "   ├── requirements-dev.txt  - Development requirements"
echo "   └── requirements-prod.txt - Production requirements"
echo "   docs/                     - Documentation"
echo "   scripts/                  - Shell scripts"
echo "   .env                      - Environment configuration"
echo ""

echo "🔧 Configuration:"
echo "   Edit .env file to add API keys:"
echo "   - OPENAI_API_KEY=your-key-here"
echo "   - CUSTOM_ENDPOINT=your-custom-endpoint-key-here"
echo "   - BRAVE_API_KEY=your-key-here"
echo "   - SERP_API_KEY=your-key-here"
echo ""

echo "🌐 Access Points:"
echo "   Default:  http://localhost:8765"
echo "   Custom:   http://localhost:YOUR_PORT"
echo ""

echo "🆘 Troubleshooting:"
echo "   Port in use:        ./scripts/restart.sh"
echo "   Missing deps:       ./scripts/setup.sh --development"
echo "   Clean install:      rm -rf .venv/ && ./scripts/setup.sh"
echo "   Update requirements: ./scripts/update_requirements.sh"
echo "   Check status:       ./scripts/status.sh"
echo ""

echo "📖 Documentation:"
echo "   Complete guide:     docs/README.md"
echo "   Requirements:       config/README-requirements.md"
echo "   Production:         docs/PRODUCTION.md"
echo "   Architecture:       docs/ARCHITECTURE.md"
