"""
Data Profiling Utilities for Kitco Research AI

This module provides data profiling capabilities with automatic fallback
to alternative packages when ydata-profiling is not available.
"""

import sys
from typing import Optional, Dict, Any, Union
import pandas as pd
from pathlib import Path
from loguru import logger


class DataProfiler:
    """
    Data profiling utility with automatic package detection and fallback.
    
    Supports multiple profiling packages:
    1. ydata-profiling (preferred, if available)
    2. sweetviz (alternative)
    3. pandas-profiling (legacy)
    4. Built-in basic profiling (fallback)
    """
    
    def __init__(self):
        self.available_packages = self._detect_available_packages()
        self.preferred_package = self._get_preferred_package()
        
        if self.preferred_package:
            logger.info(f"Data profiling using: {self.preferred_package}")
        else:
            logger.warning("No advanced profiling packages available, using basic profiling")
    
    def _detect_available_packages(self) -> Dict[str, bool]:
        """Detect which profiling packages are available."""
        packages = {}
        
        # Check for ydata-profiling
        try:
            import ydata_profiling
            packages['ydata-profiling'] = True
            logger.debug("ydata-profiling is available")
        except ImportError:
            packages['ydata-profiling'] = False
            logger.debug("ydata-profiling not available")
        
        # Check for sweetviz
        try:
            import sweetviz
            packages['sweetviz'] = True
            logger.debug("sweetviz is available")
        except ImportError:
            packages['sweetviz'] = False
            logger.debug("sweetviz not available")
        
        # Check for pandas-profiling (legacy)
        try:
            import pandas_profiling
            packages['pandas-profiling'] = True
            logger.debug("pandas-profiling is available")
        except ImportError:
            packages['pandas-profiling'] = False
            logger.debug("pandas-profiling not available")
        
        return packages
    
    def _get_preferred_package(self) -> Optional[str]:
        """Get the preferred available package."""
        # Order of preference
        preference_order = ['ydata-profiling', 'sweetviz', 'pandas-profiling']
        
        for package in preference_order:
            if self.available_packages.get(package, False):
                return package
        
        return None
    
    def profile_dataframe(self, 
                         df: pd.DataFrame, 
                         title: str = "Data Profile Report",
                         output_file: Optional[Union[str, Path]] = None,
                         **kwargs) -> Union[str, Dict[str, Any]]:
        """
        Generate a data profile report for a pandas DataFrame.
        
        Args:
            df: The DataFrame to profile
            title: Title for the report
            output_file: Optional file path to save the report
            **kwargs: Additional arguments passed to the profiling package
        
        Returns:
            Report content (HTML string or dict depending on package)
        """
        if self.preferred_package == 'ydata-profiling':
            return self._profile_with_ydata(df, title, output_file, **kwargs)
        elif self.preferred_package == 'sweetviz':
            return self._profile_with_sweetviz(df, title, output_file, **kwargs)
        elif self.preferred_package == 'pandas-profiling':
            return self._profile_with_pandas_profiling(df, title, output_file, **kwargs)
        else:
            return self._profile_basic(df, title, output_file, **kwargs)
    
    def _profile_with_ydata(self, df: pd.DataFrame, title: str, output_file: Optional[Path], **kwargs) -> str:
        """Profile using ydata-profiling."""
        try:
            from ydata_profiling import ProfileReport
            
            profile = ProfileReport(df, title=title, **kwargs)
            
            if output_file:
                profile.to_file(output_file)
                logger.info(f"Profile report saved to {output_file}")
            
            return profile.to_html()
        
        except Exception as e:
            logger.error(f"Error with ydata-profiling: {e}")
            return self._profile_basic(df, title, output_file, **kwargs)
    
    def _profile_with_sweetviz(self, df: pd.DataFrame, title: str, output_file: Optional[Path], **kwargs) -> str:
        """Profile using sweetviz."""
        try:
            import sweetviz as sv
            
            report = sv.analyze(df, **kwargs)
            
            if output_file:
                report.show_html(str(output_file))
                logger.info(f"Profile report saved to {output_file}")
            
            # Return HTML content
            return report.show_html(layout='vertical', scale=1.0)
        
        except Exception as e:
            logger.error(f"Error with sweetviz: {e}")
            return self._profile_basic(df, title, output_file, **kwargs)
    
    def _profile_with_pandas_profiling(self, df: pd.DataFrame, title: str, output_file: Optional[Path], **kwargs) -> str:
        """Profile using pandas-profiling (legacy)."""
        try:
            from pandas_profiling import ProfileReport
            
            profile = ProfileReport(df, title=title, **kwargs)
            
            if output_file:
                profile.to_file(output_file)
                logger.info(f"Profile report saved to {output_file}")
            
            return profile.to_html()
        
        except Exception as e:
            logger.error(f"Error with pandas-profiling: {e}")
            return self._profile_basic(df, title, output_file, **kwargs)
    
    def _profile_basic(self, df: pd.DataFrame, title: str, output_file: Optional[Path], **kwargs) -> Dict[str, Any]:
        """Basic profiling using pandas built-in methods."""
        logger.info("Using basic pandas profiling (no advanced packages available)")
        
        profile = {
            "title": title,
            "shape": df.shape,
            "columns": list(df.columns),
            "dtypes": df.dtypes.to_dict(),
            "missing_values": df.isnull().sum().to_dict(),
            "memory_usage": df.memory_usage(deep=True).to_dict(),
            "describe": df.describe(include='all').to_dict(),
        }
        
        # Add basic statistics for numeric columns
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            profile["numeric_summary"] = df[numeric_cols].describe().to_dict()
        
        # Add value counts for categorical columns
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        if len(categorical_cols) > 0:
            profile["categorical_summary"] = {}
            for col in categorical_cols:
                profile["categorical_summary"][col] = df[col].value_counts().head(10).to_dict()
        
        if output_file:
            import json
            with open(output_file, 'w') as f:
                json.dump(profile, f, indent=2, default=str)
            logger.info(f"Basic profile saved to {output_file}")
        
        return profile
    
    def get_available_packages(self) -> Dict[str, bool]:
        """Get information about available profiling packages."""
        return self.available_packages.copy()
    
    def get_current_package(self) -> Optional[str]:
        """Get the currently preferred package."""
        return self.preferred_package


# Global instance for easy access
_profiler = None

def get_data_profiler() -> DataProfiler:
    """Get the global data profiler instance."""
    global _profiler
    if _profiler is None:
        _profiler = DataProfiler()
    return _profiler


def profile_dataframe(df: pd.DataFrame, 
                     title: str = "Data Profile Report",
                     output_file: Optional[Union[str, Path]] = None,
                     **kwargs) -> Union[str, Dict[str, Any]]:
    """
    Convenience function to profile a DataFrame.
    
    Args:
        df: The DataFrame to profile
        title: Title for the report
        output_file: Optional file path to save the report
        **kwargs: Additional arguments passed to the profiling package
    
    Returns:
        Report content (HTML string or dict depending on available packages)
    """
    profiler = get_data_profiler()
    return profiler.profile_dataframe(df, title, output_file, **kwargs)


def check_profiling_capabilities() -> Dict[str, Any]:
    """
    Check what data profiling capabilities are available.
    
    Returns:
        Dictionary with information about available packages and capabilities
    """
    profiler = get_data_profiler()
    
    return {
        "available_packages": profiler.get_available_packages(),
        "current_package": profiler.get_current_package(),
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
        "capabilities": {
            "advanced_profiling": profiler.preferred_package is not None,
            "html_reports": profiler.preferred_package in ['ydata-profiling', 'sweetviz', 'pandas-profiling'],
            "interactive_reports": profiler.preferred_package == 'sweetviz',
            "basic_profiling": True  # Always available
        }
    }
