# src/kitco_research_ai/search_system/search_system.py
from typing import Callable, Dict

from langchain_core.language_models import BaseChatModel
from loguru import logger

from .advanced_search_system.findings.repository import FindingsRepository
from .advanced_search_system.questions.standard_question import (
    StandardQuestionGenerator,
)
from .advanced_search_system.strategies.iterdrag_strategy import IterDRAGStrategy
from .advanced_search_system.strategies.parallel_search_strategy import (
    ParallelSearchStrategy,
)
from .advanced_search_system.strategies.rapid_search_strategy import RapidSearchStrategy
from .advanced_search_system.strategies.source_based_strategy import (
    SourceBasedSearchStrategy,
)
from .advanced_search_system.strategies.standard_strategy import StandardSearchStrategy
from .citation_handler import <PERSON><PERSON><PERSON>ler
from .config.llm_config import get_llm
from .config.search_config import get_search
from .utilities.db_utils import get_db_setting
from .web_search_engines.search_engine_base import BaseSearchEngine


class AdvancedSearchSystem:
    """
    Advanced search system that coordinates different search strategies.
    """

    def __init__(
        self,
        strategy_name: str = "source-based",
        include_text_content: bool = True,
        use_cross_engine_filter: bool = True,
        llm: BaseChatModel | None = None,
        search: BaseSearchEngine | None = None,
        max_iterations: int | None = None,
        questions_per_iteration: int | None = None,
    ):
        """Initialize the advanced search system.

        Args:
            strategy_name: The name of the search strategy to use ("standard" or "iterdrag")
            include_text_content: If False, only includes metadata and links in search results
            use_cross_engine_filter: Whether to filter results across search
                engines.
            llm: LLM to use. If not provided, it will use the default one.
            search: Search engine to use. If not provided, it will use the
                default one.
            max_iterations: The maximum number of search iterations to
                perform. Will be read from the settings if not specified.
            questions_per_iteration: The number of questions to include in
                each iteration. Will be read from the settings if not specified.

        """
        # Get configuration
        self.model = llm
        if llm is None:
            self.model = get_llm()
        self.search = search
        if search is None:
            self.search = get_search(llm_instance=self.model)

        # Get iterations setting
        self.max_iterations = max_iterations
        if self.max_iterations is None:
            self.max_iterations = get_db_setting("search.iterations", 1)
        self.questions_per_iteration = questions_per_iteration
        if self.questions_per_iteration is None:
            self.questions_per_iteration = get_db_setting(
                "search.questions_per_iteration", 3
            )

        # Log the strategy name that's being used
        logger.info(
            f"Initializing AdvancedSearchSystem with strategy_name='{strategy_name}'"
        )

        # Initialize components
        self.citation_handler = CitationHandler(self.model)
        self.question_generator = StandardQuestionGenerator(self.model)
        self.findings_repository = FindingsRepository(self.model)
        # For backward compatibility
        self.questions_by_iteration = list()
        self.progress_callback = lambda _1, _2, _3: None
        self.all_links_of_system = list()

        # Initialize strategy based on name
        if strategy_name.lower() == "iterdrag":
            logger.info("Creating IterDRAGStrategy instance")
            self.strategy = IterDRAGStrategy(
                model=self.model,
                search=self.search,
                all_links_of_system=self.all_links_of_system,
            )
        elif strategy_name.lower() == "source-based":
            logger.info("Creating SourceBasedSearchStrategy instance")
            self.strategy = SourceBasedSearchStrategy(
                model=self.model,
                search=self.search,
                include_text_content=include_text_content,
                use_cross_engine_filter=use_cross_engine_filter,
                all_links_of_system=self.all_links_of_system,
            )
        elif strategy_name.lower() == "parallel":
            logger.info("Creating ParallelSearchStrategy instance")
            self.strategy = ParallelSearchStrategy(
                model=self.model,
                search=self.search,
                include_text_content=include_text_content,
                use_cross_engine_filter=use_cross_engine_filter,
                all_links_of_system=self.all_links_of_system,
            )
        elif strategy_name.lower() == "rapid":
            logger.info("Creating RapidSearchStrategy instance")
            self.strategy = RapidSearchStrategy(
                model=self.model,
                search=self.search,
                all_links_of_system=self.all_links_of_system,
            )
        else:
            logger.info("Creating StandardSearchStrategy instance")
            self.strategy = StandardSearchStrategy(
                model=self.model,
                search=self.search,
                all_links_of_system=self.all_links_of_system,
            )

        # Log the actual strategy class
        logger.info(f"Created strategy of type: {type(self.strategy).__name__}")

        # Configure the strategy with our attributes
        if hasattr(self, "progress_callback") and self.progress_callback:
            self.strategy.set_progress_callback(self.progress_callback)

    def _progress_callback(self, message: str, progress: int, metadata: dict) -> None:
        """Handle progress updates from the strategy."""
        logger.info(f"Progress: {progress}% - {message}")
        if hasattr(self, "progress_callback"):
            self.progress_callback(message, progress, metadata)

    def set_progress_callback(self, callback: Callable[[str, int, dict], None]) -> None:
        """Set a callback function to receive progress updates."""
        self.progress_callback = callback
        if hasattr(self, "strategy"):
            self.strategy.set_progress_callback(callback)

    def set_termination_check(self, check_function: Callable[[], bool]) -> None:
        """Set a function that will be called periodically to check if the research should be terminated.

        Args:
            check_function: A function that returns True if the research should be terminated
        """
        self.termination_check = check_function
        if hasattr(self, "strategy"):
            if hasattr(self.strategy, "set_termination_check"):
                self.strategy.set_termination_check(check_function)

    def analyze_topic(self, query: str) -> Dict:
        """Analyze a topic using the current strategy.

        Args:
            query: The research query to analyze
        """
        # Check if we should terminate before starting
        if hasattr(self, "termination_check") and self.termination_check():
            logger.warning("Termination requested before starting analysis")
            return {
                "findings": [],
                "iterations": 0,
                "formatted_findings": "Research was terminated before starting.",
                "current_knowledge": "",
                "error": "Research terminated",
            }

        # Get LLM info from the actual model instance if available, otherwise fall back to DB settings
        if hasattr(self.model, '_model_name') and hasattr(self.model, '_provider'):
            # Use the actual model instance information
            llm_model = getattr(self.model, '_model_name', 'unknown')
            llm_provider = getattr(self.model, '_provider', 'unknown')
        elif hasattr(self.model, 'model_name'):
            # Try alternative attribute names
            llm_model = getattr(self.model, 'model_name', 'unknown')
            llm_provider = getattr(self.model, 'provider', get_db_setting('llm.provider', 'unknown'))
        else:
            # Fall back to DB settings if model instance doesn't have the info
            llm_provider = get_db_setting('llm.provider', 'unknown')
            llm_model = get_db_setting('llm.model', 'unknown')

        search_tool = get_db_setting("search.tool", "searxng")

        # Send progress message with LLM info
        self.progress_callback(
            f"Using {llm_provider} model: {llm_model}",
            1,  # Low percentage to show this as an early step
            {
                "phase": "setup",
                "llm_info": {
                    "name": llm_model,
                    "provider": llm_provider,
                },
            },
        )
        # Send progress message with search strategy info

        self.progress_callback(
            f"Using search tool: {search_tool}",
            1.5,  # Between setup and processing steps
            {
                "phase": "setup",
                "search_info": {
                    "tool": search_tool,
                },
            },
        )

        # Check if we should terminate before starting the strategy
        if hasattr(self, "termination_check") and self.termination_check():
            logger.warning("Termination requested before starting strategy")
            return {
                "findings": [],
                "iterations": 0,
                "formatted_findings": "Research was terminated before starting analysis.",
                "current_knowledge": "",
                "error": "Research terminated",
            }

        # Use the strategy to analyze the topic
        result = self.strategy.analyze_topic(query)

        # Update our attributes for backward compatibility
        self.questions_by_iteration = self.strategy.questions_by_iteration.copy()

        # Only extend if they're different objects in memory to avoid duplication
        # This check prevents doubling the list when they reference the same object
        # Fix for issue #301: "too many links in detailed report mode"
        if id(self.all_links_of_system) != id(self.strategy.all_links_of_system):
            self.all_links_of_system.extend(self.strategy.all_links_of_system)

        # Include the search system instance for access to citations
        result["search_system"] = self
        result["all_links_of_system"] = self.all_links_of_system
        result["questions_by_iteration"] = self.questions_by_iteration
        return result
