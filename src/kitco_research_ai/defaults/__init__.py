"""
Default configuration module for Kitco Research AI.

This module is responsible for loading and initializing default
configuration files and resources used throughout the application.
"""

import logging
import os
from pathlib import Path

logger = logging.getLogger(__name__)

# Define the path to the package's defaults directory
DEFAULTS_DIR = Path(__file__).parent

# Default files available in this package
DEFAULT_FILES = {
    "main.toml": DEFAULTS_DIR / "main.toml",
    "local_collections.toml": DEFAULTS_DIR / "local_collections.toml",
    "search_engines.toml": DEFAULTS_DIR / "search_engines.toml",
}


def get_default_file_path(filename):
    """Get the path to a default configuration file."""
    if filename in DEFAULT_FILES:
        return DEFAULT_FILES[filename]
    return None


def list_default_files():
    """List all available default configuration files."""
    return list(DEFAULT_FILES.keys())


def ensure_defaults_exist():
    """Verify that all expected default files exist in the package."""
    missing = []
    for filename, filepath in DEFAULT_FILES.items():
        if not filepath.exists():
            missing.append(filename)

    if missing:
        logger.warning(
            f"The following default files are missing from the package: {', '.join(missing)}"
        )
        return False
    return True
