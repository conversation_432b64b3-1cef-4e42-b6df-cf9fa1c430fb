"""
Centralized LLM Prompts for Kitco Research AI

This module contains all LLM prompts used throughout the application,
organized by category for easy maintenance and modification.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime


class PromptManager:
    """
    Centralized manager for all LLM prompts in the application.

    This class provides a single point of access for all prompts,
    ensuring consistency and making prompt engineering easier.
    """

    def __init__(self):
        """Initialize the prompt manager."""
        pass

    # ========================================
    # QUESTION GENERATION PROMPTS
    # ========================================

    def get_standard_question_prompt(
        self,
        query: str,
        current_time: str,
        questions_per_iteration: int,
        questions_by_iteration: Optional[List] = None,
        current_knowledge: str = ""
    ) -> str:
        """
        Generate prompt for standard question generation.

        Args:
            query: The main research query
            current_time: Current date/time string
            questions_per_iteration: Number of questions to generate
            questions_by_iteration: Previous questions asked (optional)
            current_knowledge: Current accumulated knowledge (optional)

        Returns:
            Formatted prompt string
        """
        if questions_by_iteration:
            return f"""Critically reflect current knowledge (e.g., timeliness), what {questions_per_iteration} high-quality internet search questions remain unanswered to exactly answer the query?
            Query: {query}
            Today: {current_time}
            Past questions: {str(questions_by_iteration)}
            Knowledge: {current_knowledge}
            Include questions that critically reflect current knowledge.
            \n\n\nFormat: One question per line, e.g. \n Q: question1 \n Q: question2\n\n"""
        else:
            return f" You will have follow up questions. First, identify if your knowledge is outdated (high chance). Today: {current_time}. Generate {questions_per_iteration} high-quality internet search questions to exactly answer: {query}\n\n\nFormat: One question per line, e.g. \n Q: question1 \n Q: question2\n\n"

    def get_decomposition_question_prompt(
        self,
        subject: str,
        query: str,
        context: str
    ) -> str:
        """
        Generate prompt for decomposing complex queries into sub-questions.

        Args:
            subject: Extracted subject from the query
            query: Original query
            context: Additional context information

        Returns:
            Formatted prompt string
        """
        return f"""Decompose the main research topic into 3-5 specific sub-queries that can be answered independently.
Focus on breaking down complex concepts and identifying key aspects requiring separate investigation.
Ensure sub-queries are clear, targeted, and help build a comprehensive understanding.

Main Research Topic: {subject}
Original Query: {query}

Context Information:
{context[:2000]}  # Limit context length to prevent token limit issues

Your task is to create 3-5 specific questions that will help thoroughly research this topic.
If the original query is already a question, extract the core subject and formulate questions around that subject.

Return ONLY the sub-queries, one per line, without numbering or bullet points.
Example format:
What is X technology?
How does X compare to Y?
What are the security implications of X?
"""

    def get_decomposition_simple_prompt(
        self,
        topic_text: str,
        query: str
    ) -> str:
        """
        Generate simplified prompt for decomposition when complex prompt fails.

        Args:
            topic_text: Simplified topic text
            query: Original query

        Returns:
            Formatted prompt string
        """
        return f"""Break down this research topic into 3 simpler sub-questions:

Research Topic: {topic_text}
Original Query: {query}

Your task is to create 3 specific questions that will help thoroughly research this topic.
If the original query is already a question, use the core subject of that question.

Sub-questions:
1.
2.
3. """

    # ========================================
    # KNOWLEDGE GENERATION PROMPTS
    # ========================================

    def get_knowledge_generation_prompt(
        self,
        query: str,
        current_time: str,
        context: str = "",
        current_knowledge: str = "",
        questions: Optional[List[str]] = None
    ) -> str:
        """
        Generate prompt for knowledge generation.

        Args:
            query: The research query
            current_time: Current date/time string
            context: Additional context
            current_knowledge: Existing knowledge
            questions: List of questions to address

        Returns:
            Formatted prompt string
        """
        if questions:
            return f"""Based on the following query and questions, generate comprehensive knowledge:

Query: {query}
Current Time: {current_time}
Context: {context}
Current Knowledge: {current_knowledge}
Questions: {questions}

Generate detailed knowledge that:
1. Directly answers the query
2. Addresses each question
3. Includes relevant facts and details
4. Is up-to-date with current information
5. Synthesizes information from multiple sources

Format your response as a well-structured paragraph."""
        else:
            return f"""Based on the following query, generate comprehensive knowledge:

Query: {query}
Current Time: {current_time}
Context: {context}
Current Knowledge: {current_knowledge}

Generate detailed knowledge that:
1. Directly answers the query
2. Includes relevant facts and details
3. Is up-to-date with current information
4. Synthesizes information from multiple sources

Format your response as a well-structured paragraph."""

    def get_sub_knowledge_prompt(
        self,
        sub_query: str,
        context: str = ""
    ) -> str:
        """
        Generate prompt for sub-question knowledge generation.

        Args:
            sub_query: The sub-question to answer
            context: Additional context

        Returns:
            Formatted prompt string
        """
        return f"""Generate comprehensive knowledge to answer this sub-question:

Sub-question: {sub_query}

{context}

Generate detailed knowledge that:
1. Directly answers the sub-question
2. Includes relevant facts and details
3. Is up-to-date with current information
4. Synthesizes information from multiple sources

Format your response as a well-structured paragraph."""

    def get_knowledge_compression_prompt(
        self,
        current_knowledge: str,
        query: str
    ) -> str:
        """
        Generate prompt for compressing accumulated knowledge.

        Args:
            current_knowledge: Knowledge to compress
            query: Original research query

        Returns:
            Formatted prompt string
        """
        return f"""Compress the following accumulated knowledge relevant to the query '{query}'.
Retain the key facts, findings, and citations. Remove redundancy.

Accumulated Knowledge:
{current_knowledge}

Compressed Knowledge:"""

    # ========================================
    # ANALYSIS & SYNTHESIS PROMPTS
    # ========================================

    def get_synthesis_prompt(
        self,
        query: str,
        current_knowledge: str,
        sub_queries: List[str]
    ) -> str:
        """
        Generate prompt for synthesizing findings into final answer.

        Args:
            query: Original research query
            current_knowledge: Accumulated knowledge to synthesize
            sub_queries: List of sub-questions that were asked

        Returns:
            Formatted prompt string
        """
        return f"""Use IEEE style citations [1], [2], etc. Never make up your own citations. Synthesize the following accumulated knowledge into a comprehensive answer for the original query.
Format the response with clear sections, citations, and a concise summary.

Original Query: {query}

Accumulated Knowledge:
{current_knowledge}

Sub-questions asked (for context):
{chr(10).join(f"- {sq}" for sq in sub_queries)}

Generate a well-structured, concise answer that:
1. Starts with a clear explanation of the most important points
2. Organizes information into logical sections with headers if needed
3. Maintains logical flow and prioritizes important information over minor details
4. Avoids repetition and unnecessary detail

Use IEEE style citations [1], [2], etc. Never make up your own citations.
"""

    def get_document_analysis_prompt(
        self,
        query: str,
        docs_text: str
    ) -> str:
        """
        Generate prompt for analyzing documents.

        Args:
            query: The search query
            docs_text: Text content of documents to analyze

        Returns:
            Formatted prompt string
        """
        return f"""Analyze these document excerpts related to the query: "{query}"

    {docs_text}

    Provide a concise summary of the key information found in these documents related to the query.
    """

    # ========================================
    # CITATION & FACT-CHECKING PROMPTS
    # ========================================

    def get_initial_citation_prompt(
        self,
        query: str,
        formatted_sources: str
    ) -> str:
        """
        Generate prompt for initial citation analysis.

        Args:
            query: The research question
            formatted_sources: Formatted source documents with numbers

        Returns:
            Formatted prompt string
        """
        return f"""Analyze the following information concerning the question and include citations using numbers in square brackets [1], [2], etc. When citing, use the source number provided at the start of each source.

Question: {query}

Sources:
{formatted_sources}

Provide a detailed analysis with citations. Do not create the bibliography, it will be provided automatically.  Never make up sources. Never write or create urls. Only write text relevant to the question. Example format: "According to the research [1], ..."
"""

    def get_fact_check_prompt(
        self,
        previous_knowledge: str,
        formatted_sources: str
    ) -> str:
        """
        Generate prompt for fact-checking sources.

        Args:
            previous_knowledge: Previously accumulated knowledge
            formatted_sources: New sources to fact-check

        Returns:
            Formatted prompt string
        """
        return f"""Analyze these sources for factual consistency:
1. Cross-reference major claims between sources
2. Identify and flag any contradictions
3. Verify basic facts (dates, company names, ownership)
4. Note when sources disagree

Previous Knowledge:
{previous_knowledge}

New Sources:
{formatted_sources}

        Return any inconsistencies or conflicts found."""

    def get_followup_citation_prompt(
        self,
        question: str,
        previous_knowledge: str,
        formatted_sources: str,
        fact_check_response: str
    ) -> str:
        """
        Generate prompt for follow-up citation analysis.

        Args:
            question: The follow-up question
            previous_knowledge: Previously accumulated knowledge
            formatted_sources: New formatted sources
            fact_check_response: Results from fact-checking

        Returns:
            Formatted prompt string
        """
        return f"""Using the previous knowledge and new sources, answer the question. Include citations using numbers in square brackets [1], [2], etc. When citing, use the source number provided at the start of each source. Reflect information from sources critically.

Previous Knowledge:
{previous_knowledge}

Question: {question}

New Sources:
{formatted_sources}
Reflect information from sources critically based on: {fact_check_response}. Never invent sources.
Provide a detailed answer with citations.  Example format: "According to [1], ..." """

    # ========================================
    # REPORT GENERATION PROMPTS
    # ========================================

    def get_report_structure_prompt(
        self,
        query: str,
        combined_content: str
    ) -> str:
        """
        Generate prompt for determining report structure.

        Args:
            query: The research query
            combined_content: Summary of research content

        Returns:
            Formatted prompt string
        """
        return f"""
        Analyze this research content about: {query}

        Content Summary:
        {combined_content[:1000]}... [truncated]

        Determine the most appropriate report structure by:
        1. Analyzing the type of content (technical, business, academic, etc.)
        2. Identifying main themes and logical groupings
        3. Considering the depth and breadth of the research

        Return a table of contents structure in this exact format:
        STRUCTURE
        1. [Section Name]
           - [Subsection] | [purpose]
        2. [Section Name]
           - [Subsection] | [purpose]
        ...
        END_STRUCTURE

        Make the structure specific to the content, not generic.
        Each subsection must include its purpose after the | symbol.
        """

    # ========================================
    # SEARCH ENGINE OPTIMIZATION PROMPTS
    # ========================================

    def get_guardian_query_optimization_prompt(
        self,
        query: str
    ) -> str:
        """
        Generate prompt for optimizing queries for Guardian news search.

        Args:
            query: Original natural language query

        Returns:
            Formatted prompt string
        """
        return f"""Transform this natural language question into a very short Guardian news search query.

Original question: "{query}"

INSTRUCTIONS:
1. Extract key news-relevant terms and proper nouns
2. Remove question words (what, how, why, when, where)
3. Focus on newsworthy topics, people, events, or policies
4. Keep it under 6 words if possible
5. Use terms that would appear in news headlines

EXAMPLES:
"What is happening with Brexit negotiations?" → "Brexit negotiations"
"How is climate change affecting the economy?" → "climate change economy"
"What did Biden say about immigration?" → "Biden immigration"

Return ONLY the extremely brief search query.
"""

    def get_semantic_scholar_optimization_prompt(
        self,
        query: str
    ) -> str:
        """
        Generate prompt for optimizing queries for Semantic Scholar academic search.

        Args:
            query: Original natural language query

        Returns:
            Formatted prompt string
        """
        return f"""Transform this natural language question into an optimized academic search query.

Original query: "{query}"

INSTRUCTIONS:
1. Extract key academic concepts, technical terms, and proper nouns
2. Remove generic words, filler words, and non-technical terms
3. Add quotation marks around specific phrases that should be kept together
4. Return ONLY the optimized search query with no explanation
5. Keep it under 100 characters if possible

EXAMPLE TRANSFORMATIONS:
"What are the latest findings about mRNA vaccines and COVID-19?" → "mRNA vaccines COVID-19 recent findings"
"How does machine learning impact climate change prediction?" → "machine learning "climate change" prediction"
"Tell me about quantum computing approaches for encryption" → "quantum computing encryption"

Return ONLY the optimized search query with no explanation.
"""

    def get_semantic_scholar_fallback_prompt(
        self,
        original_query: str
    ) -> str:
        """
        Generate prompt for creating fallback queries when no results found.

        Args:
            original_query: The original query that returned no results

        Returns:
            Formatted prompt string
        """
        return f"""You are helping refine a search query that returned no results.

Original failed query: "{original_query}"

Generate 3 alternative academic search queries that focus on:
1. Core concepts only (remove modifiers)
2. Broader related terms
3. Different technical terminology

Format each query on a new line with no numbering or explanation. Keep each query under 8 words and very focused.
"""

    # ========================================
    # FILTERING & RANKING PROMPTS
    # ========================================

    def get_url_filtering_prompt(
        self,
        query: str,
        results_context: str
    ) -> str:
        """
        Generate prompt for filtering URLs based on relevance.

        Args:
            query: The search query
            results_context: Context about the search results

        Returns:
            Formatted prompt string
        """
        return f"""You are a search result filter. Analyze these search results for the query: "{query}"

{results_context}

Return a JSON array of indices (0-based) for results that are:
1. Directly relevant to the query
2. From reliable sources
3. Contain substantial content (not just ads or navigation pages)
4. Are accessible and not broken

Example: [0, 2, 4, 7] for keeping results at positions 0, 2, 4, and 7.

Respond with ONLY the JSON array, no other text."""

    def get_cross_engine_ranking_prompt(
        self,
        query: str,
        context: str,
        max_results: int
    ) -> str:
        """
        Generate prompt for ranking search results from multiple engines.

        Args:
            query: The search query
            context: Context about all search results
            max_results: Maximum number of results to keep

        Returns:
            Formatted prompt string
        """
        return f"""You are a search result filter. Your task is to rank search results from multiple engines by relevance to the query: "{query}"

{context}

Instructions:
1. Rank results by relevance to the query (most relevant first)
2. Consider source credibility and content quality
3. Avoid duplicate or very similar results
4. Prioritize results with substantial, unique information
5. Return the top {max_results} results maximum

Return a JSON array of indices (0-based) in order of relevance.
Example: [5, 2, 8, 1, 12] for the 5 most relevant results in that order.

Respond with ONLY the JSON array, no other text."""

    # ========================================
    # UTILITY METHODS
    # ========================================

    def get_current_time_string(self) -> str:
        """
        Get current time as formatted string for prompts.

        Returns:
            Current date in YYYY-MM-DD format
        """
        return datetime.now().strftime("%Y-%m-%d")

    def validate_prompt_variables(self, prompt_name: str, **kwargs) -> bool:
        """
        Validate that all required variables are provided for a prompt.

        Args:
            prompt_name: Name of the prompt method
            **kwargs: Variables to validate

        Returns:
            True if all required variables are present, False otherwise
        """
        # Define required variables for each prompt type
        required_vars = {
            'get_standard_question_prompt': ['query', 'current_time', 'questions_per_iteration'],
            'get_decomposition_question_prompt': ['subject', 'query', 'context'],
            'get_knowledge_generation_prompt': ['query', 'current_time'],
            'get_synthesis_prompt': ['query', 'current_knowledge', 'sub_queries'],
            'get_initial_citation_prompt': ['query', 'formatted_sources'],
            'get_report_structure_prompt': ['query', 'combined_content'],
            # Add more as needed
        }

        if prompt_name not in required_vars:
            return True  # Unknown prompt, assume valid

        required = required_vars[prompt_name]
        missing = [var for var in required if var not in kwargs or kwargs[var] is None]

        if missing:
            print(f"Warning: Missing required variables for {prompt_name}: {missing}")
            return False

        return True


# Create a global instance for easy access
prompt_manager = PromptManager()
