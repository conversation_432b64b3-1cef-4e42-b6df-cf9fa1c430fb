# Centralized LLM Prompts System

This directory contains the centralized prompt management system for the Kitco Research AI project. All LLM prompts have been consolidated into a single, maintainable location.

## Overview

The centralized prompt system provides:
- **Single source of truth** for all LLM prompts
- **Easy prompt engineering** and modifications
- **Consistent formatting** across the application
- **Type safety** with parameter validation
- **Backward compatibility** with existing code

## Files

- `llm_prompts.py` - Main prompt manager with all prompts
- `__init__.py` - Package initialization and exports
- `README.md` - This documentation file

## Usage

### Basic Usage

```python
from kitco_research_ai.prompts.llm_prompts import prompt_manager

# Generate a standard question prompt
prompt = prompt_manager.get_standard_question_prompt(
    query="What is machine learning?",
    current_time="2024-01-15",
    questions_per_iteration=3
)

# Use with your LLM
response = llm.invoke(prompt)
```

### Available Prompt Categories

#### 1. Question Generation Prompts
- `get_standard_question_prompt()` - Generate follow-up research questions
- `get_decomposition_question_prompt()` - Break down complex queries into sub-questions
- `get_decomposition_simple_prompt()` - Simplified decomposition for fallback

#### 2. Knowledge Generation Prompts
- `get_knowledge_generation_prompt()` - Generate comprehensive knowledge
- `get_sub_knowledge_prompt()` - Answer specific sub-questions
- `get_knowledge_compression_prompt()` - Compress accumulated knowledge

#### 3. Analysis & Synthesis Prompts
- `get_synthesis_prompt()` - Synthesize findings into final answer
- `get_document_analysis_prompt()` - Analyze document content

#### 4. Citation & Fact-Checking Prompts
- `get_initial_citation_prompt()` - Initial citation analysis
- `get_fact_check_prompt()` - Cross-reference sources for consistency
- `get_followup_citation_prompt()` - Follow-up citation analysis

#### 5. Report Generation Prompts
- `get_report_structure_prompt()` - Determine optimal report structure

#### 6. Search Engine Optimization Prompts
- `get_guardian_query_optimization_prompt()` - Optimize for Guardian news search
- `get_semantic_scholar_optimization_prompt()` - Optimize for academic search
- `get_semantic_scholar_fallback_prompt()` - Generate fallback queries

#### 7. Filtering & Ranking Prompts
- `get_url_filtering_prompt()` - Filter URLs by relevance
- `get_cross_engine_ranking_prompt()` - Rank results from multiple engines

### Parameter Validation

The prompt manager includes built-in validation:

```python
# Validate required parameters
is_valid = prompt_manager.validate_prompt_variables(
    'get_standard_question_prompt',
    query="test",
    current_time="2024-01-15",
    questions_per_iteration=3
)
```

### Utility Methods

```python
# Get current time string for prompts
current_time = prompt_manager.get_current_time_string()
```

## Migration Summary

The following files were successfully migrated to use centralized prompts:

### ✅ Migrated Files:
1. **Question Generation:**
   - `src/kitco_research_ai/advanced_search_system/questions/standard_question.py`
   - `src/kitco_research_ai/advanced_search_system/questions/decomposition_question.py`

2. **Knowledge Generation:**
   - `src/kitco_research_ai/advanced_search_system/knowledge/standard_knowledge.py`

3. **Analysis & Synthesis:**
   - `src/kitco_research_ai/advanced_search_system/findings/repository.py`
   - `src/kitco_research_ai/citation_handler.py`
   - `src/kitco_research_ai/api/research_functions.py`

4. **Report Generation:**
   - `src/kitco_research_ai/report_generator.py`

### ✅ Migration Benefits:
- **No breaking changes** - All existing functionality preserved
- **Improved maintainability** - Single location for all prompts
- **Enhanced consistency** - Standardized prompt formatting
- **Easier debugging** - Centralized prompt validation
- **Future-proof** - Easy to add new prompts or modify existing ones

## Adding New Prompts

To add a new prompt:

1. Add a new method to the `PromptManager` class in `llm_prompts.py`
2. Follow the naming convention: `get_[category]_[purpose]_prompt()`
3. Include proper type hints and docstrings
4. Add parameter validation if needed
5. Update this README with the new prompt

Example:
```python
def get_new_analysis_prompt(self, data: str, context: str = "") -> str:
    """
    Generate prompt for new analysis task.
    
    Args:
        data: The data to analyze
        context: Additional context (optional)
        
    Returns:
        Formatted prompt string
    """
    return f"""Analyze the following data:
    
Data: {data}
Context: {context}

Provide detailed analysis including:
1. Key patterns identified
2. Important insights
3. Recommendations

Format your response clearly."""
```

## Best Practices

1. **Always use the prompt manager** instead of hardcoded prompts
2. **Validate parameters** for critical prompts
3. **Include context** when available to improve prompt quality
4. **Use descriptive variable names** in prompt templates
5. **Test prompts** after modifications to ensure they work correctly

## Troubleshooting

### Common Issues:

1. **Import Error**: Make sure you're importing from the correct path:
   ```python
   from kitco_research_ai.prompts.llm_prompts import prompt_manager
   ```

2. **Missing Parameters**: Check the method signature and provide all required parameters

3. **Validation Warnings**: Use the validation method to check your parameters:
   ```python
   prompt_manager.validate_prompt_variables('method_name', **your_params)
   ```

## Testing

The centralized prompt system has been thoroughly tested with:
- ✅ All prompt generation methods
- ✅ Parameter validation
- ✅ Integration with existing components
- ✅ Backward compatibility
- ✅ Error handling

All tests pass successfully, confirming that the migration maintains full functionality while providing the benefits of centralization.
