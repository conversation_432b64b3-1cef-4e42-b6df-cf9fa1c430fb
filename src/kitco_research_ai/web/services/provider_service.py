#!/usr/bin/env python3
"""
Provider Service - Web Interface for Provider Management
========================================================

This service provides a web-friendly interface for managing LLM providers,
including configuration, validation, and hot swapping capabilities.

Features:
- Provider configuration management
- Real-time availability checking
- Model discovery and caching
- Hot swapping support
- Web UI integration
"""

from typing import Any, Dict, List, Optional, Tuple
from loguru import logger
from dataclasses import asdict

from ...config.provider_registry import get_provider_registry, ProviderConfig
from .settings_service import get_setting, set_setting


class ProviderService:
    """
    Service for managing LLM providers in the web interface.
    
    Provides high-level operations for provider management, configuration,
    and integration with the web UI and database settings.
    """
    
    def __init__(self):
        """Initialize the provider service."""
        self.registry = get_provider_registry()
    
    def get_available_providers_for_ui(self) -> Dict[str, Dict[str, Any]]:
        """
        Get available providers formatted for UI display.
        
        Returns:
            Dictionary of providers with UI-friendly information
        """
        providers = self.registry.get_available_providers(check_health=True)
        ui_providers = {}
        
        for provider_id, provider in providers.items():
            ui_providers[provider_id] = {
                'id': provider_id,
                'name': provider.name,
                'description': provider.description,
                'type': provider.type,
                'available': provider.available,
                'priority': provider.priority,
                'capabilities': asdict(provider.capabilities),
                'has_api_key': bool(self.registry.get_provider_api_key(provider_id)),
                'models': self.registry.get_provider_models(provider_id),
                'default_model': provider.models.get('default', ''),
                'error_message': provider.error_message
            }
        
        return ui_providers
    
    def get_provider_details(self, provider_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific provider.
        
        Args:
            provider_id: Provider identifier
            
        Returns:
            Detailed provider information or None if not found
        """
        provider = self.registry.get_provider(provider_id)
        if not provider:
            return None
        
        # Check current availability
        health_status = self.registry.check_provider_health([provider_id])
        
        return {
            'id': provider_id,
            'name': provider.name,
            'description': provider.description,
            'type': provider.type,
            'enabled': provider.enabled,
            'available': health_status.get(provider_id, False),
            'priority': provider.priority,
            'hidden': provider.hidden,
            'capabilities': asdict(provider.capabilities),
            'parameters': provider.parameters,
            'api_config': {
                'base_url': self.registry.get_provider_base_url(provider_id),
                'has_api_key': bool(self.registry.get_provider_api_key(provider_id)),
                'api_key_required': provider.api.get('api_key_required', True)
            },
            'models': {
                'available': self.registry.get_provider_models(provider_id),
                'default': provider.models.get('default', ''),
                'dynamic_discovery': provider.models.get('dynamic_discovery', False)
            },
            'error_message': provider.error_message,
            'last_checked': provider.last_checked
        }
    
    def get_current_provider(self) -> str:
        """
        Get the currently selected provider.
        
        Returns:
            Current provider ID
        """
        return get_setting('llm.provider', self.registry.get_default_provider())
    
    def set_current_provider(self, provider_id: str) -> Tuple[bool, str]:
        """
        Set the current provider (hot swap).
        
        Args:
            provider_id: Provider identifier
            
        Returns:
            Tuple of (success, message)
        """
        # Validate provider exists and is available
        provider = self.registry.get_provider(provider_id)
        if not provider:
            return False, f"Provider '{provider_id}' not found"
        
        if not provider.enabled:
            return False, f"Provider '{provider_id}' is disabled"
        
        # Check availability
        health_status = self.registry.check_provider_health([provider_id])
        if not health_status.get(provider_id, False):
            return False, f"Provider '{provider_id}' is not available: {provider.error_message or 'Health check failed'}"
        
        # Update database setting
        success = set_setting('llm.provider', provider_id)
        if success:
            logger.info(f"Provider switched to: {provider_id}")
            return True, f"Successfully switched to {provider.name}"
        else:
            return False, "Failed to update provider setting"
    
    def get_current_model(self) -> str:
        """
        Get the currently selected model.
        
        Returns:
            Current model name
        """
        provider_id = self.get_current_provider()
        provider = self.registry.get_provider(provider_id)
        default_model = provider.models.get('default', '') if provider else ''
        return get_setting('llm.model', default_model)
    
    def set_current_model(self, model_name: str) -> Tuple[bool, str]:
        """
        Set the current model.
        
        Args:
            model_name: Model name
            
        Returns:
            Tuple of (success, message)
        """
        # Validate model is available for current provider
        provider_id = self.get_current_provider()
        available_models = self.registry.get_provider_models(provider_id)
        
        if available_models and model_name not in available_models:
            return False, f"Model '{model_name}' not available for provider '{provider_id}'"
        
        # Update database setting
        success = set_setting('llm.model', model_name)
        if success:
            logger.info(f"Model switched to: {model_name}")
            return True, f"Successfully switched to model {model_name}"
        else:
            return False, "Failed to update model setting"
    
    def refresh_provider_models(self, provider_id: str) -> Tuple[bool, List[str]]:
        """
        Refresh available models for a provider.
        
        Args:
            provider_id: Provider identifier
            
        Returns:
            Tuple of (success, list_of_models)
        """
        try:
            models = self.registry.get_provider_models(provider_id, refresh=True)
            return True, models
        except Exception as e:
            logger.error(f"Failed to refresh models for {provider_id}: {e}")
            return False, []
    
    def validate_provider_configuration(self, provider_id: str) -> Dict[str, Any]:
        """
        Validate a provider's configuration.
        
        Args:
            provider_id: Provider identifier
            
        Returns:
            Validation results
        """
        is_valid, errors = self.registry.validate_provider_config(provider_id)
        
        # Additional checks
        provider = self.registry.get_provider(provider_id)
        warnings = []
        
        if provider:
            # Check API key for cloud providers
            if provider.type == 'cloud' and not self.registry.get_provider_api_key(provider_id):
                warnings.append("API key not configured")
            
            # Check base URL for local providers
            if provider.type == 'local' and not self.registry.get_provider_base_url(provider_id):
                warnings.append("Base URL not configured")
        
        return {
            'valid': is_valid,
            'errors': errors,
            'warnings': warnings,
            'provider_id': provider_id
        }
    
    def get_provider_configuration_template(self, provider_id: str) -> Dict[str, Any]:
        """
        Get configuration template for a provider.
        
        Args:
            provider_id: Provider identifier
            
        Returns:
            Configuration template for UI forms
        """
        provider = self.registry.get_provider(provider_id)
        if not provider:
            return {}
        
        template = {
            'provider_id': provider_id,
            'name': provider.name,
            'description': provider.description,
            'type': provider.type,
            'fields': []
        }
        
        # Add API key field if required
        if provider.api.get('api_key_required', True):
            template['fields'].append({
                'name': 'api_key',
                'label': 'API Key',
                'type': 'password',
                'required': True,
                'env_var': provider.api.get('api_key_env', ''),
                'db_key': provider.api.get('api_key_db', ''),
                'description': 'API key for authentication'
            })
        
        # Add base URL field for configurable endpoints
        if provider.api.get('base_url_env') or provider.api.get('base_url_db'):
            template['fields'].append({
                'name': 'base_url',
                'label': 'Base URL',
                'type': 'url',
                'required': provider.type == 'local',
                'env_var': provider.api.get('base_url_env', ''),
                'db_key': provider.api.get('base_url_db', ''),
                'default': provider.api.get('base_url_default', ''),
                'description': 'API endpoint URL'
            })
        
        # Add model selection
        template['fields'].append({
            'name': 'model',
            'label': 'Model',
            'type': 'select',
            'required': True,
            'options': self.registry.get_provider_models(provider_id),
            'default': provider.models.get('default', ''),
            'description': 'AI model to use'
        })
        
        return template
    
    def check_all_providers_health(self) -> Dict[str, bool]:
        """
        Check health status of all providers.
        
        Returns:
            Dictionary mapping provider IDs to health status
        """
        return self.registry.check_provider_health()
    
    def reload_providers(self) -> bool:
        """
        Reload provider configurations from file.
        
        Returns:
            Success status
        """
        try:
            self.registry.load_providers(force_reload=True)
            logger.info("Provider configurations reloaded")
            return True
        except Exception as e:
            logger.error(f"Failed to reload providers: {e}")
            return False


# Global service instance
_service_instance: Optional[ProviderService] = None


def get_provider_service() -> ProviderService:
    """Get the global provider service instance."""
    global _service_instance
    if _service_instance is None:
        _service_instance = ProviderService()
    return _service_instance
