{% extends "base.html" %}
{% from "components/custom_dropdown.html" import render_dropdown %}

{% set active_page = 'new-research' %}

{% block title %}New Research - Kitco Research AI{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="{{ url_for('research.serve_static', path='css/custom_dropdown.css') }}">
{% endblock %}

{% block content %}
<div class="page active" id="new-research">
    <div class="page-header">
        <h1>Start New Research</h1>
    </div>
    <!-- Add the alert container -->
    <div id="research-alert" class="settings-alert-container" style="display:none"></div>
    <div class="card research-card">
        <div class="card-content">
            <form id="research-form">
                <div class="form-group">
                    <label for="query">Research Query</label>
                    <textarea id="query" name="query" rows="3" placeholder="Enter your research topic or question..."></textarea>
                </div>
                <div class="form-group">
                    <label>Research Mode</label>
                    <div class="mode-selection">
                        <div class="mode-option active" data-mode="quick">
                            <div class="mode-icon"><i class="fas fa-bolt"></i></div>
                            <div class="mode-info">
                                <h3>Quick Summary</h3>
                                <p>Generated in a few minutes</p>
                            </div>
                        </div>
                        <div class="mode-option" data-mode="detailed">
                            <div class="mode-icon"><i class="fas fa-microscope"></i></div>
                            <div class="mode-info">
                                <h3>Detailed Report</h3>
                                <p>In-depth analysis (takes longer)</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Options -->
                <div class="advanced-options-toggle">
                    <span class="toggle-text">Advanced Options</span>
                    <i class="fas fa-chevron-down"></i>
                </div>

                <div class="advanced-options-panel">
                    <div class="form-row">
                        <!-- Model Provider Selection -->
                        <div class="form-group half">
                            <label for="model_provider">Model Provider</label>
                            <select id="model_provider" name="model_provider" class="form-control">
                                <!-- Will be populated dynamically -->
                                <option value="">Loading providers...</option>
                            </select>
                            <span class="input-help">Select the LLM provider to use</span>
                        </div>

                        <!-- Placeholder for future expansion -->
                        <div class="form-group half">
                            <!-- Reserved for future provider-specific settings -->
                        </div>
                    </div>

                    <div class="form-row">
                        <!-- Model Selection -->
                        <div class="form-group half">
                            {{ render_dropdown(
                                input_id="model",
                                dropdown_id="model-dropdown",
                                placeholder="Enter or select a model",
                                label="Language Model",
                                help_text="Select or enter a custom model name",
                                show_refresh=True,
                                refresh_aria_label="Refresh model list"
                            ) }}
                        </div>

                        <!-- Search Engine Selection -->
                        <div class="form-group half">
                            {{ render_dropdown(
                                input_id="search_engine",
                                dropdown_id="search-engine-dropdown",
                                placeholder="Select a search engine",
                                label="Search Engine",
                                help_text="Select the search engine to use for research",
                                show_refresh=True,
                                refresh_aria_label="Refresh search engine list"
                            ) }}
                        </div>
                    </div>

                    <div class="form-row">
                        <!-- Search Iterations -->
                        <div class="form-group half">
                            <label for="iterations">Search Iterations</label>
                            <input type="number" id="iterations" name="iterations" class="form-control" min="1" max="5" value="2">
                            <span class="input-help">Number of research cycles to perform</span>
                        </div>

                        <!-- Questions Per Iteration -->
                        <div class="form-group half">
                            <label for="questions_per_iteration">Questions Per Iteration</label>
                            <input type="number" id="questions_per_iteration" name="questions_per_iteration" class="form-control" min="1" max="10" value="3">
                            <span class="input-help">Follow-up questions in each cycle</span>
                        </div>
                    </div>
                </div>

                <div class="form-options">
                    <div class="form-option">
                        <label for="notification-toggle" class="checkbox-label">
                            <input type="checkbox" id="notification-toggle" checked>
                            <span class="checkbox-text">Sound notifications when complete</span>
                        </label>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" id="start-research-btn" disabled><i class="fas fa-rocket"></i> Start Research</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block component_scripts %}
<script src="{{ url_for('research.serve_static', path='js/components/custom_dropdown.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/components/research.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/research_form.js') }}"></script>
{% endblock %}
