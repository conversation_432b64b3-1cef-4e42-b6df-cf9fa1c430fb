{% extends "base.html" %}

{% block title %}Research Results - Kitco Research AI{% endblock %}

{% block content %}
<div class="page active" id="research-results">
    <div class="page-header">
        <div class="results-header">
            <h1 class="page-title">Research Results</h1>
            <div class="results-actions">
                <button class="btn btn-outline" id="download-pdf-btn"><i class="fas fa-file-pdf"></i> Download PDF</button>
                <button class="btn btn-outline" id="export-markdown-btn"><i class="fas fa-file-alt"></i> Export Markdown</button>
                <button class="btn btn-outline" id="back-to-history"><i class="fas fa-arrow-left"></i> Back to History</button>
            </div>
        </div>
    </div>
    <div class="card results-card">
        <div class="card-content">
            <div class="results-metadata" id="research-metadata">
                <div class="metadata-item">
                    <span class="metadata-label">Query:</span>
                    <span id="result-query" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Generated:</span>
                    <span id="result-date" class="metadata-value"></span>
                </div>
                <div class="metadata-item">
                    <span class="metadata-label">Mode:</span>
                    <span id="result-mode" class="metadata-value"></span>
                </div>
            </div>
            <div class="results-content" id="results-content">
                <!-- Will be populated dynamically -->
                <div class="loading-spinner centered">
                    <div class="spinner"></div>
                </div>
            </div>
        </div>
    </div>

    {% include "components/log_panel.html" %}
</div>
{% endblock %}

{% block page_scripts %}
<!-- Fallback utilities in case main services are not available -->
<script src="{{ url_for('research.serve_static', path='js/components/fallback/ui.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/components/fallback/formatting.js') }}"></script>

<!-- Main services -->
<script src="{{ url_for('research.serve_static', path='js/services/ui.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/services/formatting.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/services/api.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/services/pdf.js') }}"></script>

<!-- Results component -->
<script src="{{ url_for('research.serve_static', path='js/components/results.js') }}"></script>
{% endblock %} 