<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{% block title %}Kitco Research AI{% endblock %}</title>
    <link rel="stylesheet" href="{{ url_for('research.serve_static', path='css/styles.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" integrity="sha512-Fo3rlrZj/k7ujTnHg4CGR2D7kSs0v4LLanw2qksYuRlEzO+tcaEPQogQ0KaoGN26/zrn20ImR1DfuLWnOo7aBA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github-dark.min.css" integrity="sha512-rO+olRTkcf/42UQVblSWtuKmQNcunYVnxgAJjSBHE25OWDQsrOnKUG2xDet4eA2NDbsrD5xf4pMkZbB0k6jmRA==" crossorigin="anonymous" referrerpolicy="no-referrer">
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        {% include 'components/sidebar.html' %}

        <!-- Main Content -->
        <main class="main-content">
            {% block content %}{% endblock %}

            <!-- Collapsible Log Panel is included in specific pages -->
        </main>
    </div>

    <!-- Mobile Tab Bar -->
    {% include 'components/mobile_nav.html' %}

    <!-- Common Templates -->
    {% block templates %}{% endblock %}

    <!-- Library Manager (Load First) -->
    <script src="{{ url_for('research.serve_static', path='js/services/library_manager.js') }}"></script>

    <!-- Core Libraries with Fallback Management -->
    <script>
        // Initialize library loading on page load
        document.addEventListener('DOMContentLoaded', function() {
            if (window.libraryManager) {
                window.libraryManager.loadAllLibraries().then(function(results) {
                    console.log('📚 Library loading completed:', results);

                    // Configure libraries after loading
                    configureLibraries();

                    // Dispatch custom event to signal libraries are ready
                    document.dispatchEvent(new CustomEvent('librariesReady', { detail: results }));
                }).catch(function(error) {
                    console.error('❌ Library loading failed:', error);
                });
            }
        });

        function configureLibraries() {
            // Configure marked to not use eval
            if (typeof marked !== 'undefined') {
                marked.setOptions({
                    headerIds: false,
                    mangle: false,
                    smartypants: false
                });
                console.log('✅ Marked configured');
            }

            // Configure html2canvas to avoid using eval if possible
            if (typeof html2canvas !== 'undefined') {
                window.html2canvas_noSandbox = true;
                console.log('✅ html2canvas configured');
            }

            // Configure highlight.js if available
            if (typeof hljs !== 'undefined') {
                hljs.configure({
                    ignoreUnescapedHTML: true
                });
                console.log('✅ highlight.js configured');
            }
        }
    </script>

    <!-- Core JS Services -->
    <script src="{{ url_for('research.serve_static', path='js/services/sanitizer.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/services/formatting.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/services/ui.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/services/api.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/services/socket.js') }}"></script>

    <!-- Shared Components -->
    <script src="{{ url_for('research.serve_static', path='js/components/logpanel.js') }}"></script>
    <script src="{{ url_for('research.serve_static', path='js/components/library_status.js') }}"></script>

    <!-- Page-specific Components -->
    {% block component_scripts %}{% endblock %}

    <!-- Page-specific JS -->
    {% block page_scripts %}{% endblock %}



    <script src="/research/static/js/components/settings_sync.js"></script>
</body>
</html>
