{% extends "base.html" %}
{% from "components/settings_form.html" import render_setting %}
{% from "components/custom_dropdown.html" import render_dropdown %}

{% set active_page = 'settings' %}

{% block title %}Settings - Kitco Research AI{% endblock %}

{% block extra_head %}
<meta name="csrf-token" content="{{ csrf_token() }}">
<link rel="stylesheet" href="{{ url_for('research.serve_static', path='css/settings.css') }}">
<link rel="stylesheet" href="{{ url_for('research.serve_static', path='css/custom_dropdown.css') }}">
{% endblock %}

{% block content %}
<div class="page active" id="settings">
    <div class="settings-container">
        <div class="page-header">
            <h1>Settings</h1>
            <p class="settings-description">
                Configure your research environment by adjusting the settings below. All settings are automatically saved when you make changes.
            </p>


        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        <i class="fas {% if category == 'success' %}fa-check-circle{% else %}fa-exclamation-circle{% endif %}"></i> {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div id="settings-alert" style="display:none"></div>

        <div class="card">
            <div class="card-content">
                <div class="search-controls">
                    <input type="text" id="settings-search" class="search-input" placeholder="Search settings by name, description or category...">
                </div>

                <div class="settings-tabs">
                    <div class="settings-tab active" data-tab="all">All Settings</div>
                    <div class="settings-tab" data-tab="llm">Language Models</div>
                    <div class="settings-tab" data-tab="search">Search Engines</div>
                    <div class="settings-tab" data-tab="report">Reports</div>
                    <div class="settings-tab" data-tab="app">Application</div>
                </div>

                <form id="settings-form" class="settings-form">
                    <div id="settings-content">
                        <div class="loading-spinner centered">
                            <div class="spinner"></div>
                            <p>Loading settings...</p>
                        </div>
                    </div>

                    <div class="toggle-raw-config" id="toggle-raw-config" style="display: none;">
                        <i class="fas fa-code"></i> <span id="toggle-text">Show JSON Configuration</span>
                    </div>

                    <div id="raw-config" class="raw-config-section" style="display: none;">
                        <div class="section-header">
                            <h3>Advanced JSON Configuration</h3>
                            <p class="section-description">
                                Use this editor to directly modify configuration values. You can add new parameters not shown in the UI, and they will be preserved across saves. Changes here override the UI settings.
                            </p>
                        </div>
                        <div id="json-editor-container">
                            <textarea id="raw_config_editor" class="json-editor"></textarea>
                        </div>
                    </div>

                    <div class="form-actions" style="display: none;">
                        <button type="button" id="reset-to-defaults-button" class="btn btn-warning">
                            <i class="fas fa-sync-alt"></i> Reset to Defaults
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save All Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block page_scripts %}
<script src="{{ url_for('research.serve_static', path='js/components/custom_dropdown.js') }}"></script>
<script src="{{ url_for('research.serve_static', path='js/components/settings.js') }}"></script>
{% endblock %}
