import os
import sys

from loguru import logger

from ..setup_data_dir import setup_data_dir
from ..utilities.db_utils import get_db_setting
from .app_factory import create_app
from .models.database import (
    DB_PATH,
    LEGACY_DEEP_RESEARCH_DB,
    LEGACY_RESEARCH_HISTORY_DB,
)

# Ensure data directory exists
setup_data_dir()

# Run schema upgrades if database exists
if os.path.exists(DB_PATH):
    try:
        logger.info("Running schema upgrades on existing database")
        from .database.schema_upgrade import run_schema_upgrades

        run_schema_upgrades()
    except Exception:
        logger.exception("Error running schema upgrades")
        logger.warning("Continuing without schema upgrades")


# Check if we need to run database migration
def check_migration_needed():
    """Check if database migration is needed, based on presence of legacy files and absence of new DB"""
    if not os.path.exists(DB_PATH):
        # The new database doesn't exist, check if legacy databases exist
        legacy_files_exist = os.path.exists(LEGACY_DEEP_RESEARCH_DB) or os.path.exists(
            LEGACY_RESEARCH_HISTORY_DB
        )

        if legacy_files_exist:
            logger.info(
                "Legacy database files found, but ldr.db doesn't exist. Migration needed."
            )
            return True

    return False


# Create the Flask app and SocketIO instance
app, socketio = create_app()


@logger.catch()
def main():
    """
    Entry point for the web application when run as a command.
    This function is needed for the package's entry point to work properly.
    """
    # Check if legacy databases exist and warn user
    if check_migration_needed():
        logger.warning(
            "Legacy database files detected. Starting with fresh database."
        )
        print("=" * 80)
        print("LEGACY DATABASE FILES DETECTED")
        print(
            "Legacy database files were found, but the application now uses a unified database."
        )
        print(
            "The application will start with a fresh database. Your previous data is preserved"
        )
        print(
            "in the legacy files but won't be automatically imported."
        )
        print("=" * 80)

    # Get web server settings with defaults
    port = get_db_setting("application.technical_settings.port", 8765)
    host = get_db_setting("application.technical_settings.host", "0.0.0.0")
    debug = get_db_setting("application.technical_settings.debug", False)

    # Override port if specified in command line arguments
    for arg in sys.argv:
        if arg.startswith("--port="):
            port = int(arg.split("=")[1])
        elif arg == "--port" and sys.argv.index(arg) + 1 < len(sys.argv):
            port = int(sys.argv[sys.argv.index(arg) + 1])

    logger.info(f"Starting web server on {host}:{port} (debug: {debug})")
    socketio.run(app, debug=debug, host=host, port=port, allow_unsafe_werkzeug=True)


if __name__ == "__main__":
    main()
