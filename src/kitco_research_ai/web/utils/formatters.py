import logging
import traceback

# Initialize logger
logger = logging.getLogger(__name__)


def convert_debug_to_markdown(raw_text, query):
    """
    Convert the debug-formatted text to clean markdown.

    Args:
        raw_text: The raw formatted findings with debug symbols
        query: Original research query

    Returns:
        Clean markdown formatted text
    """
    try:
        logger.info(f"Starting markdown conversion for query: {query}")
        logger.info(f"Raw text type: {type(raw_text)}")

        # Handle None or empty input
        if not raw_text:
            logger.warning("WARNING: raw_text is empty or None")
            return f"No detailed findings available for '{query}'."

        # If there's a "DETAILED FINDINGS:" section, extract everything after it
        if "DETAILED FINDINGS:" in raw_text:
            logger.info("Found DETAILED FINDINGS section")
            detailed_index = raw_text.index("DETAILED FINDINGS:")
            content = raw_text[detailed_index + len("DETAILED FINDINGS:") :].strip()
        else:
            logger.info("No DETAILED FINDINGS section found, using full text")
            content = raw_text

        # Remove divider lines with === symbols
        lines_before = len(content.split("\n"))
        content = "\n".join(
            [
                line
                for line in content.split("\n")
                if not line.strip().startswith("===") and not line.strip() == "=" * 80
            ]
        )
        lines_after = len(content.split("\n"))
        logger.info(f"Removed {lines_before - lines_after} divider lines")

        # Remove SEARCH QUESTIONS BY ITERATION section
        if "SEARCH QUESTIONS BY ITERATION:" in content:
            logger.info("Found SEARCH QUESTIONS BY ITERATION section")
            search_index = content.index("SEARCH QUESTIONS BY ITERATION:")
            next_major_section = -1
            for marker in ["DETAILED FINDINGS:", "COMPLETE RESEARCH:"]:
                if marker in content[search_index:]:
                    marker_pos = content.index(marker, search_index)
                    if next_major_section == -1 or marker_pos < next_major_section:
                        next_major_section = marker_pos

            if next_major_section != -1:
                logger.info(
                    f"Removing section from index {search_index} to {next_major_section}"
                )
                content = content[:search_index] + content[next_major_section:]
            else:
                # If no later section, just remove everything from SEARCH QUESTIONS onwards
                logger.info(f"Removing everything after index {search_index}")
                content = content[:search_index].strip()

        logger.info(f"Final markdown length: {len(content.strip())}")
        return content.strip()
    except Exception as e:
        logger.error(f"Error in convert_debug_to_markdown: {str(e)}")
        logger.error(traceback.format_exc())
        # Return a basic message with the original query as fallback
        return f"# Research on {query}\n\nThere was an error formatting the research results."
