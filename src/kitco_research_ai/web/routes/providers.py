#!/usr/bin/env python3
"""
Provider Management Routes
=========================

Flask routes for managing LLM providers through the web interface.
Provides endpoints for provider configuration, switching, and monitoring.
"""

from flask import Blueprint, jsonify, request, render_template
from loguru import logger

from ..services.provider_service import get_provider_service
from ..services.settings_service import get_setting, set_setting

# Create blueprint
providers_bp = Blueprint('providers', __name__, url_prefix='/api/providers')


@providers_bp.route('/', methods=['GET'])
def list_providers():
    """Get list of all available providers."""
    try:
        service = get_provider_service()
        providers = service.get_available_providers_for_ui()
        
        return jsonify({
            'success': True,
            'providers': providers,
            'current_provider': service.get_current_provider(),
            'current_model': service.get_current_model()
        })
    except Exception as e:
        logger.error(f"Error listing providers: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/<provider_id>', methods=['GET'])
def get_provider_details(provider_id: str):
    """Get detailed information about a specific provider."""
    try:
        service = get_provider_service()
        details = service.get_provider_details(provider_id)
        
        if details is None:
            return jsonify({
                'success': False,
                'error': f'Provider {provider_id} not found'
            }), 404
        
        return jsonify({
            'success': True,
            'provider': details
        })
    except Exception as e:
        logger.error(f"Error getting provider details for {provider_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/current', methods=['GET'])
def get_current_provider():
    """Get the currently active provider and model."""
    try:
        service = get_provider_service()
        
        return jsonify({
            'success': True,
            'provider': service.get_current_provider(),
            'model': service.get_current_model()
        })
    except Exception as e:
        logger.error(f"Error getting current provider: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/current', methods=['POST'])
def set_current_provider():
    """Set the current provider (hot swap)."""
    try:
        data = request.get_json()
        if not data or 'provider' not in data:
            return jsonify({
                'success': False,
                'error': 'Provider ID is required'
            }), 400
        
        provider_id = data['provider']
        service = get_provider_service()
        
        success, message = service.set_current_provider(provider_id)
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'provider': provider_id
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400
            
    except Exception as e:
        logger.error(f"Error setting current provider: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/current/model', methods=['POST'])
def set_current_model():
    """Set the current model."""
    try:
        data = request.get_json()
        if not data or 'model' not in data:
            return jsonify({
                'success': False,
                'error': 'Model name is required'
            }), 400
        
        model_name = data['model']
        service = get_provider_service()
        
        success, message = service.set_current_model(model_name)
        
        if success:
            return jsonify({
                'success': True,
                'message': message,
                'model': model_name
            })
        else:
            return jsonify({
                'success': False,
                'error': message
            }), 400
            
    except Exception as e:
        logger.error(f"Error setting current model: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/<provider_id>/models', methods=['GET'])
def get_provider_models(provider_id: str):
    """Get available models for a provider."""
    try:
        service = get_provider_service()
        refresh = request.args.get('refresh', 'false').lower() == 'true'
        
        if refresh:
            success, models = service.refresh_provider_models(provider_id)
            if not success:
                return jsonify({
                    'success': False,
                    'error': f'Failed to refresh models for {provider_id}'
                }), 500
        else:
            from ...config.provider_registry import get_provider_registry
            registry = get_provider_registry()
            models = registry.get_provider_models(provider_id)
        
        return jsonify({
            'success': True,
            'provider': provider_id,
            'models': models
        })
    except Exception as e:
        logger.error(f"Error getting models for {provider_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/<provider_id>/validate', methods=['POST'])
def validate_provider(provider_id: str):
    """Validate a provider's configuration."""
    try:
        service = get_provider_service()
        validation_result = service.validate_provider_configuration(provider_id)
        
        return jsonify({
            'success': True,
            'validation': validation_result
        })
    except Exception as e:
        logger.error(f"Error validating provider {provider_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/health', methods=['GET'])
def check_providers_health():
    """Check health status of all providers."""
    try:
        service = get_provider_service()
        health_status = service.check_all_providers_health()
        
        return jsonify({
            'success': True,
            'health_status': health_status
        })
    except Exception as e:
        logger.error(f"Error checking providers health: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/reload', methods=['POST'])
def reload_providers():
    """Reload provider configurations from file."""
    try:
        service = get_provider_service()
        success = service.reload_providers()
        
        if success:
            return jsonify({
                'success': True,
                'message': 'Provider configurations reloaded successfully'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to reload provider configurations'
            }), 500
            
    except Exception as e:
        logger.error(f"Error reloading providers: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@providers_bp.route('/<provider_id>/config', methods=['GET'])
def get_provider_config_template(provider_id: str):
    """Get configuration template for a provider."""
    try:
        service = get_provider_service()
        template = service.get_provider_configuration_template(provider_id)
        
        if not template:
            return jsonify({
                'success': False,
                'error': f'Provider {provider_id} not found'
            }), 404
        
        return jsonify({
            'success': True,
            'template': template
        })
    except Exception as e:
        logger.error(f"Error getting config template for {provider_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


# Error handlers
@providers_bp.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    return jsonify({
        'success': False,
        'error': 'Endpoint not found'
    }), 404


@providers_bp.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    return jsonify({
        'success': False,
        'error': 'Internal server error'
    }), 500
