import json
import logging

import requests
from flask import Blueprint, current_app, jsonify, request

from ...utilities.url_utils import normalize_url
from ..models.database import get_db_connection
from ..routes.research_routes import active_research, termination_flags
from ..services.research_service import (
    cancel_research,
    run_research_process,
    start_research_process,
)
from ..services.resource_service import (
    add_resource,
    delete_resource,
    get_resources_for_research,
)

# Create blueprint
api_bp = Blueprint("api", __name__)
logger = logging.getLogger(__name__)


# API Routes
@api_bp.route("/start", methods=["POST"])
def api_start_research():
    """
    Start a new research process
    """
    data = request.json
    query = data.get("query", "")
    mode = data.get("mode", "quick")

    if not query:
        return jsonify({"status": "error", "message": "Query is required"}), 400

    # Validate and normalize research mode
    if mode:
        mode = mode.lower().strip()
        if mode not in ["quick", "detailed"]:
            return jsonify({
                "status": "error",
                "message": f"Invalid research mode '{mode}'. Must be 'quick' or 'detailed'."
            }), 400

    try:
        # Create a record in the database with explicit UTC timestamp
        from datetime import datetime

        created_at = datetime.utcnow().isoformat()

        conn = get_db_connection()
        cursor = conn.cursor()

        # Save basic research settings for API route
        research_settings = {
            "model_provider": "OLLAMA",  # Default
            "model": "llama2",  # Default
            "search_engine": "searxng",  # Default - we've removed wikipedia, arxiv, etc.
        }

        cursor.execute(
            "INSERT INTO research_history (query, mode, status, created_at, progress_log, metadata) VALUES (?, ?, ?, ?, ?, ?)",
            (
                query,
                mode,
                "in_progress",
                created_at,
                json.dumps(
                    [{"time": created_at, "message": "Research started", "progress": 0}]
                ),
                json.dumps(research_settings),
            ),
        )
        research_id = cursor.lastrowid
        conn.commit()
        conn.close()

        # Start the research process
        research_thread = start_research_process(
            research_id,
            query,
            mode,
            active_research,
            termination_flags,
            run_research_process,
        )

        # Store the thread reference
        active_research[research_id]["thread"] = research_thread

        return jsonify(
            {
                "status": "success",
                "message": "Research started successfully",
                "research_id": research_id,
            }
        )
    except Exception as e:
        logger.error(f"Error starting research: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/status/<int:research_id>", methods=["GET"])
def api_research_status(research_id):
    """
    Get the status of a research process
    """
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            "SELECT status, progress, completed_at, report_path, metadata FROM research_history WHERE id = ?",
            (research_id,),
        )
        result = cursor.fetchone()
        conn.close()

        if result is None:
            return jsonify({"error": "Research not found"}), 404

        status, progress, completed_at, report_path, metadata_str = result

        # Parse metadata if it exists
        metadata = {}
        if metadata_str:
            try:
                metadata = json.loads(metadata_str)
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON in metadata for research {research_id}")

        return jsonify(
            {
                "status": status,
                "progress": progress,
                "completed_at": completed_at,
                "report_path": report_path,
                "metadata": metadata,
            }
        )
    except Exception as e:
        logger.error(f"Error getting research status: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/terminate/<int:research_id>", methods=["POST"])
def api_terminate_research(research_id):
    """
    Terminate a research process
    """
    try:
        result = cancel_research(research_id)
        return jsonify(
            {"status": "success", "message": "Research terminated", "result": result}
        )
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/resources/<int:research_id>", methods=["GET"])
def api_get_resources(research_id):
    """
    Get resources for a specific research
    """
    try:
        resources = get_resources_for_research(research_id)
        return jsonify({"status": "success", "resources": resources})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/resources/<int:research_id>", methods=["POST"])
def api_add_resource(research_id):
    """
    Add a new resource to a research project
    """
    try:
        data = request.json

        # Required fields
        title = data.get("title")
        url = data.get("url")

        # Optional fields
        content_preview = data.get("content_preview")
        source_type = data.get("source_type", "web")
        metadata = data.get("metadata", {})

        # Validate required fields
        if not title or not url:
            return (
                jsonify({"status": "error", "message": "Title and URL are required"}),
                400,
            )

        # Check if the research exists
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT id FROM research_history WHERE id = ?", (research_id,))
        result = cursor.fetchone()
        conn.close()

        if not result:
            return jsonify({"status": "error", "message": "Research not found"}), 404

        # Add the resource
        resource_id = add_resource(
            research_id=research_id,
            title=title,
            url=url,
            content_preview=content_preview,
            source_type=source_type,
            metadata=metadata,
        )

        return jsonify(
            {
                "status": "success",
                "message": "Resource added successfully",
                "resource_id": resource_id,
            }
        )
    except Exception as e:
        logger.error(f"Error adding resource: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route(
    "/resources/<int:research_id>/delete/<int:resource_id>", methods=["DELETE"]
)
def api_delete_resource(research_id, resource_id):
    """
    Delete a resource from a research project
    """
    try:
        # Delete the resource
        success = delete_resource(resource_id)

        if success:
            return jsonify(
                {"status": "success", "message": "Resource deleted successfully"}
            )
        else:
            return jsonify({"status": "error", "message": "Resource not found"}), 404
    except Exception as e:
        logger.error(f"Error deleting resource: {str(e)}")
        return jsonify({"status": "error", "message": str(e)}), 500


@api_bp.route("/check/ollama_status", methods=["GET"])
def check_ollama_status():
    """
    Check if Ollama API is running
    """
    try:
        # Get Ollama URL from config
        llm_config = current_app.config.get("LLM_CONFIG", {})
        provider = llm_config.get("provider", "ollama")

        if provider.lower() != "ollama":
            return jsonify(
                {"running": True, "message": f"Using provider: {provider}, not Ollama"}
            )

        # Get Ollama API URL from LLM config
        raw_ollama_base_url = llm_config.get(
            "ollama_base_url", "http://localhost:11434"
        )
        ollama_base_url = (
            normalize_url(raw_ollama_base_url)
            if raw_ollama_base_url
            else "http://localhost:11434"
        )

        logger.info(f"Checking Ollama status at: {ollama_base_url}")

        # Check if Ollama is running
        try:
            response = requests.get(f"{ollama_base_url}/api/tags", timeout=5)

            # Add response details for debugging
            logger.debug(f"Ollama status check response code: {response.status_code}")

            if response.status_code == 200:
                # Try to validate the response content
                try:
                    data = response.json()

                    # Check the format
                    if "models" in data:
                        model_count = len(data.get("models", []))
                        logger.info(
                            f"Ollama service is running with {model_count} models (new API format)"
                        )
                    else:
                        # Older API format
                        model_count = len(data)
                        logger.info(
                            f"Ollama service is running with {model_count} models (old API format)"
                        )

                    return jsonify(
                        {
                            "running": True,
                            "message": f"Ollama service is running with {model_count} models",
                            "model_count": model_count,
                        }
                    )
                except ValueError as json_err:
                    logger.warning(f"Ollama returned invalid JSON: {json_err}")
                    # It's running but returned invalid JSON
                    return jsonify(
                        {
                            "running": True,
                            "message": "Ollama service is running but returned invalid data format",
                            "error_details": str(json_err),
                        }
                    )
            else:
                logger.warning(
                    f"Ollama returned non-200 status code: {response.status_code}"
                )
                return jsonify(
                    {
                        "running": False,
                        "message": f"Ollama service returned status code: {response.status_code}",
                        "status_code": response.status_code,
                    }
                )

        except requests.exceptions.ConnectionError as conn_err:
            logger.warning(f"Ollama connection error: {conn_err}")
            return jsonify(
                {
                    "running": False,
                    "message": "Ollama service is not running or not accessible",
                    "error_type": "connection_error",
                    "error_details": str(conn_err),
                }
            )
        except requests.exceptions.Timeout as timeout_err:
            logger.warning(f"Ollama request timed out: {timeout_err}")
            return jsonify(
                {
                    "running": False,
                    "message": "Ollama service request timed out after 5 seconds",
                    "error_type": "timeout",
                    "error_details": str(timeout_err),
                }
            )

    except Exception as e:
        logger.error(f"Error checking Ollama status: {str(e)}")
        import traceback

        logger.error(f"Traceback: {traceback.format_exc()}")
        return jsonify(
            {
                "running": False,
                "message": f"Error checking Ollama: {str(e)}",
                "error_type": "exception",
                "error_details": str(e),
            }
        )


@api_bp.route("/check/ollama_model", methods=["GET"])
def check_ollama_model():
    """
    Check if the configured Ollama model is available
    """
    try:
        # Get Ollama configuration
        llm_config = current_app.config.get("LLM_CONFIG", {})
        provider = llm_config.get("provider", "ollama")

        if provider.lower() != "ollama":
            return jsonify(
                {
                    "available": True,
                    "message": f"Using provider: {provider}, not Ollama",
                    "provider": provider,
                }
            )

        # Get model name from request or use config default
        model_name = request.args.get("model")
        if not model_name:
            model_name = llm_config.get("model", "gemma3:12b")

        # Log which model we're checking for debugging
        logger.info(f"Checking availability of Ollama model: {model_name}")

        # Get Ollama API URL from LLM config
        raw_ollama_base_url = llm_config.get(
            "ollama_base_url", "http://localhost:11434"
        )
        ollama_base_url = (
            normalize_url(raw_ollama_base_url)
            if raw_ollama_base_url
            else "http://localhost:11434"
        )

        # Check if the model is available
        try:
            response = requests.get(f"{ollama_base_url}/api/tags", timeout=5)

            # Log response details for debugging
            logger.debug(f"Ollama API response status: {response.status_code}")

            if response.status_code != 200:
                logger.warning(
                    f"Ollama API returned non-200 status: {response.status_code}"
                )
                return jsonify(
                    {
                        "available": False,
                        "model": model_name,
                        "message": f"Could not access Ollama service - status code: {response.status_code}",
                        "status_code": response.status_code,
                    }
                )

            # Try to parse the response
            try:
                data = response.json()

                # Debug log the first bit of the response
                response_preview = (
                    str(data)[:500] + "..." if len(str(data)) > 500 else str(data)
                )
                logger.debug(f"Ollama API response data: {response_preview}")

                # Get models based on API format
                models = []
                if "models" in data:
                    # Newer Ollama API
                    logger.debug("Using new Ollama API format (models key)")
                    models = data.get("models", [])
                else:
                    # Older Ollama API format
                    logger.debug("Using old Ollama API format (array)")
                    models = data

                # Log available models for debugging
                model_names = [m.get("name", "") for m in models]
                logger.debug(
                    f"Available Ollama models: {', '.join(model_names[:10])}"
                    + (
                        f" and {len(model_names) - 10} more"
                        if len(model_names) > 10
                        else ""
                    )
                )

                # Case-insensitive model name comparison
                model_exists = any(
                    m.get("name", "").lower() == model_name.lower() for m in models
                )

                if model_exists:
                    logger.info(f"Ollama model {model_name} is available")
                    return jsonify(
                        {
                            "available": True,
                            "model": model_name,
                            "message": f"Model {model_name} is available",
                            "all_models": model_names,
                        }
                    )
                else:
                    # Check if models were found at all
                    if not models:
                        logger.warning("No models found in Ollama")
                        message = "No models found in Ollama. Please pull models first."
                    else:
                        logger.warning(
                            f"Model {model_name} not found among {len(models)} available models"
                        )
                        message = (
                            f"Model {model_name} is not available. Available models: "
                            + ", ".join(model_names[:5])
                        ) + (
                            f" and {len(model_names) - 5} more"
                            if len(model_names) > 5
                            else ""
                        )

                    return jsonify(
                        {
                            "available": False,
                            "model": model_name,
                            "message": message,
                            "all_models": model_names,
                        }
                    )
            except ValueError as json_err:
                # JSON parsing error
                logger.error(f"Failed to parse Ollama API response: {json_err}")
                return jsonify(
                    {
                        "available": False,
                        "model": model_name,
                        "message": f"Invalid response from Ollama API: {json_err}",
                        "error_type": "json_parse_error",
                    }
                )

        except requests.exceptions.ConnectionError as conn_err:
            # Connection error
            logger.warning(f"Connection error to Ollama API: {conn_err}")
            return jsonify(
                {
                    "available": False,
                    "model": model_name,
                    "message": "Could not connect to Ollama service",
                    "error_type": "connection_error",
                    "error_details": str(conn_err),
                }
            )
        except requests.exceptions.Timeout:
            # Timeout error
            logger.warning("Timeout connecting to Ollama API")
            return jsonify(
                {
                    "available": False,
                    "model": model_name,
                    "message": "Connection to Ollama service timed out",
                    "error_type": "timeout",
                }
            )

    except Exception as e:
        # General exception
        logger.error(f"Error checking Ollama model: {e}")
        import traceback

        logger.error(f"Traceback: {traceback.format_exc()}")

        return jsonify(
            {
                "available": False,
                "model": (
                    model_name
                    if "model_name" in locals()
                    else llm_config.get("model", "gemma3:12b")
                ),
                "message": f"Error checking model: {str(e)}",
                "error_type": "exception",
                "error_details": str(e),
            }
        )


# Helper route to get system configuration
@api_bp.route("/config", methods=["GET"])
def api_get_config():
    """
    Get public system configuration
    """
    # Only return public configuration
    public_config = {
        "version": current_app.config.get("VERSION", "0.1.0"),
        "llm_provider": current_app.config.get("LLM_CONFIG", {}).get(
            "provider", "ollama"
        ),
        "search_tool": current_app.config.get("SEARCH_CONFIG", {}).get(
            "search_tool", "auto"
        ),
        "features": {
            "notifications": current_app.config.get("ENABLE_NOTIFICATIONS", False)
        },
    }

    return jsonify(public_config)
