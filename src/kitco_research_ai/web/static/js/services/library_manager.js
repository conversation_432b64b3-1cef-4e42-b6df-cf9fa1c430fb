/**
 * Library Manager Service
 * Ensures all external libraries are properly loaded and available
 */

window.libraryManager = (function() {
    // Track loaded libraries
    const loadedLibraries = new Set();
    const failedLibraries = new Set();
    const loadingPromises = new Map();
    
    // Library definitions with CDN and fallback URLs
    const libraries = {
        'socket.io': {
            test: () => typeof io !== 'undefined',
            urls: [
                'https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.4.1/socket.io.min.js',
                'https://cdn.socket.io/4.4.1/socket.io.min.js'
            ],
            integrity: 'sha512-XMXVpLLwEkCtkNNSKSyZhC+3+ZCEf0b/rDNzJtHw0Z8cqUhYVw2JtROcQOuXkp7/84I9d/82DFG+8yZYC3VtWA==',
            required: true
        },
        'marked': {
            test: () => typeof marked !== 'undefined' && typeof marked.parse === 'function',
            urls: [
                'https://cdn.jsdelivr.net/npm/marked@4.3.0/lib/marked.umd.min.js',
                'https://cdnjs.cloudflare.com/ajax/libs/marked/4.3.0/marked.min.js'
            ],
            integrity: 'sha512-mfmrAr62ob+CiJ4heDp0LrREPbOUAoaCBgQRGnO2pOqI9MvIqMmqKClpqnMjn2mpmMClp5ZZeWPELFQNMNd2Yw==',
            required: true
        },
        'highlight.js': {
            test: () => typeof hljs !== 'undefined',
            urls: [
                'https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js',
                'https://cdn.jsdelivr.net/gh/highlightjs/cdn-release@11.7.0/build/highlight.min.js'
            ],
            integrity: 'sha512-bgHRAiTjGrzHzLyKOnpFvaEpGzJet3z4tZnXGjpsCcqOnAH6VGUx9frc5bcIhKTVLEiCO6vEhNAgx5jtLUYrfA==',
            required: false
        },
        'jspdf': {
            test: () => typeof window.jspdf !== 'undefined' && typeof window.jspdf.jsPDF === 'function',
            urls: [
                'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js',
                'https://cdn.jsdelivr.net/npm/jspdf@2.5.1/dist/jspdf.umd.min.js'
            ],
            integrity: 'sha512-qZvrmS2ekKPF2mSznTQsxqPgnpkI4DNTlrdUmTzrDgektczlKNRRhy5X5AAOnx5S09ydFYWWNSfcEqDTTHgtNA==',
            required: false
        },
        'html2canvas': {
            test: () => typeof html2canvas !== 'undefined',
            urls: [
                'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js',
                'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js'
            ],
            integrity: 'sha512-BNaRQnYJYiPSqHHDb58B0yaPfCu+Wgds8Gp/gU33kqBtgNS4tSPHuGibyoeqMV/TJlSKda6FXzoEyYGjTe+vXA==',
            required: false
        }
    };

    /**
     * Load a single library
     * @param {string} name - Library name
     * @param {Object} config - Library configuration
     * @returns {Promise<boolean>} Success status
     */
    function loadLibrary(name, config) {
        // Check if already loaded
        if (config.test()) {
            loadedLibraries.add(name);
            console.log(`✅ Library ${name} already available`);
            return Promise.resolve(true);
        }

        // Check if already loading
        if (loadingPromises.has(name)) {
            return loadingPromises.get(name);
        }

        // Check if previously failed
        if (failedLibraries.has(name)) {
            console.warn(`⚠️ Library ${name} previously failed to load`);
            return Promise.resolve(false);
        }

        console.log(`📦 Loading library: ${name}`);

        const promise = loadLibraryFromUrls(name, config);
        loadingPromises.set(name, promise);
        
        return promise;
    }

    /**
     * Try loading library from multiple URLs
     * @param {string} name - Library name
     * @param {Object} config - Library configuration
     * @returns {Promise<boolean>} Success status
     */
    async function loadLibraryFromUrls(name, config) {
        for (let i = 0; i < config.urls.length; i++) {
            const url = config.urls[i];
            console.log(`🔄 Trying to load ${name} from: ${url}`);
            
            try {
                await loadScript(url, config.integrity && i === 0 ? config.integrity : null);
                
                // Wait a bit for the library to initialize
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // Test if library is now available
                if (config.test()) {
                    loadedLibraries.add(name);
                    console.log(`✅ Successfully loaded ${name} from ${url}`);
                    return true;
                }
            } catch (error) {
                console.warn(`❌ Failed to load ${name} from ${url}:`, error.message);
            }
        }

        // All URLs failed
        failedLibraries.add(name);
        console.error(`💥 Failed to load ${name} from all sources`);
        return false;
    }

    /**
     * Load a script from URL
     * @param {string} url - Script URL
     * @param {string|null} integrity - SRI integrity hash
     * @returns {Promise<void>}
     */
    function loadScript(url, integrity = null) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = url;
            script.async = false;
            
            if (integrity) {
                script.integrity = integrity;
                script.crossOrigin = 'anonymous';
            }

            script.onload = () => resolve();
            script.onerror = () => reject(new Error(`Failed to load script: ${url}`));

            // Add timeout
            const timeout = setTimeout(() => {
                reject(new Error(`Timeout loading script: ${url}`));
            }, 10000);

            script.onload = () => {
                clearTimeout(timeout);
                resolve();
            };

            script.onerror = () => {
                clearTimeout(timeout);
                reject(new Error(`Failed to load script: ${url}`));
            };

            document.head.appendChild(script);
        });
    }

    /**
     * Load all required libraries
     * @returns {Promise<Object>} Results object with success/failure counts
     */
    async function loadAllLibraries() {
        console.log('🚀 Starting library loading process...');
        
        const results = {
            loaded: [],
            failed: [],
            skipped: []
        };

        // Load libraries in parallel
        const loadPromises = Object.entries(libraries).map(async ([name, config]) => {
            try {
                const success = await loadLibrary(name, config);
                if (success) {
                    results.loaded.push(name);
                } else {
                    if (config.required) {
                        results.failed.push(name);
                    } else {
                        results.skipped.push(name);
                    }
                }
            } catch (error) {
                console.error(`Error loading ${name}:`, error);
                if (config.required) {
                    results.failed.push(name);
                } else {
                    results.skipped.push(name);
                }
            }
        });

        await Promise.all(loadPromises);

        // Log results
        console.log(`📊 Library loading complete:
✅ Loaded: ${results.loaded.join(', ') || 'none'}
❌ Failed: ${results.failed.join(', ') || 'none'}
⏭️ Skipped: ${results.skipped.join(', ') || 'none'}`);

        return results;
    }

    /**
     * Load specific libraries on demand
     * @param {string[]} libraryNames - Names of libraries to load
     * @returns {Promise<boolean>} True if all requested libraries loaded successfully
     */
    async function loadSpecificLibraries(libraryNames) {
        console.log(`📦 Loading specific libraries: ${libraryNames.join(', ')}`);
        
        const promises = libraryNames.map(name => {
            if (libraries[name]) {
                return loadLibrary(name, libraries[name]);
            } else {
                console.warn(`⚠️ Unknown library: ${name}`);
                return Promise.resolve(false);
            }
        });

        const results = await Promise.all(promises);
        return results.every(result => result === true);
    }

    /**
     * Check if a library is available
     * @param {string} name - Library name
     * @returns {boolean} True if library is available
     */
    function isLibraryAvailable(name) {
        if (libraries[name]) {
            return libraries[name].test();
        }
        return false;
    }

    /**
     * Get status of all libraries
     * @returns {Object} Status object
     */
    function getLibraryStatus() {
        const status = {};
        Object.keys(libraries).forEach(name => {
            status[name] = {
                available: isLibraryAvailable(name),
                loaded: loadedLibraries.has(name),
                failed: failedLibraries.has(name),
                required: libraries[name].required
            };
        });
        return status;
    }

    // Public API
    return {
        loadAllLibraries,
        loadSpecificLibraries,
        isLibraryAvailable,
        getLibraryStatus,
        loadLibrary: (name) => libraries[name] ? loadLibrary(name, libraries[name]) : Promise.resolve(false)
    };
})();
