/**
 * Utility functions for formatting data
 */

/**
 * Format a status string to be more user-friendly
 * @param {string} status - The status string
 * @returns {string} The formatted status string
 */
function formatStatus(status) {
    switch(status) {
        case 'in_progress': return 'In Progress';
        case 'completed': return 'Completed';
        case 'failed': return 'Failed';
        case 'suspended': return 'Suspended';
        default: return status.charAt(0).toUpperCase() + status.slice(1);
    }
}

/**
 * Format a research mode string to be more user-friendly
 * @param {string} mode - The mode string
 * @returns {string} The formatted mode string
 */
function formatMode(mode) {
    switch(mode) {
        case 'quick': return 'Quick Summary';
        case 'detailed': return 'Detailed Report';
        default: return mode.charAt(0).toUpperCase() + mode.slice(1);
    }
}

/**
 * Format a date string with optional duration
 * @param {string} date - The date string in ISO format
 * @param {number|null} duration - Optional duration in seconds
 * @returns {string} The formatted date string
 */
function formatDate(date, duration = null) {
    if (!date) return 'Unknown';
    
    try {
        const dateObj = new Date(date);
        const options = { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        
        let formattedDate = dateObj.toLocaleDateString('en-US', options);
        
        if (duration) {
            // Format the duration
            const minutes = Math.floor(duration / 60);
            const seconds = duration % 60;
            
            if (minutes > 0) {
                formattedDate += ` (${minutes}m ${seconds}s)`;
            } else {
                formattedDate += ` (${seconds}s)`;
            }
        }
        
        return formattedDate;
    } catch (e) {
        console.error('Error formatting date:', e);
        return date; // Return the original date if there's an error
    }
}

/**
 * Format a duration in seconds to a readable string
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration string
 */
function formatDuration(seconds) {
    if (!seconds || isNaN(seconds)) return 'Unknown';
    
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    
    if (minutes === 0) {
        return `${remainingSeconds}s`;
    } else {
        return `${minutes}m ${remainingSeconds}s`;
    }
}

/**
 * Capitalize the first letter of a string
 * @param {string} string - The string to capitalize
 * @returns {string} The capitalized string
 */
function capitalizeFirstLetter(string) {
    if (!string) return '';
    return string.charAt(0).toUpperCase() + string.slice(1);
}

/**
 * Format a number with thousands separators
 * @param {number} num - The number to format
 * @returns {string} Formatted number
 */
function formatNumber(num) {
    if (num === null || num === undefined) return '0';
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Export the functions to make them available to other modules
window.formatting = {
    formatStatus,
    formatMode,
    formatDate,
    formatDuration,
    formatNumber,
    capitalizeFirstLetter
}; 