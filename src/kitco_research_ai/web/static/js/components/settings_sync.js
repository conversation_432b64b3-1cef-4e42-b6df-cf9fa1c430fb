// Function to save settings using the settings page endpoint
function saveMenuSettings(settingKey, settingValue) {
    // Create payload with the single setting being changed
    const payload = {};
    payload[settingKey] = settingValue;

    console.log('Saving setting:', settingKey, '=', settingValue);

    // Get CSRF token
    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Use fetch to send to the same endpoint as the settings page
    fetch('/research/settings/save_all_settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(payload)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Show success notification if UI module is available
            if (window.ui && window.ui.showMessage) {
                window.ui.showMessage('Setting saved successfully', 'success');
            } else {
                console.log('Setting saved successfully:', data);
            }
        } else {
            // Show error notification
            if (window.ui && window.ui.showMessage) {
                window.ui.showMessage('Failed to save setting: ' + (data.message || 'Unknown error'), 'error');
            } else {
                console.error('Failed to save setting:', data);
            }
        }
    })
    .catch(error => {
        console.error('Error saving setting:', error);
        if (window.ui && window.ui.showMessage) {
            window.ui.showMessage('Error saving setting', 'error');
        }
    });
}

/**
 * Connects the menu settings to use the same save method as the settings page.
 */
function connectMenuSettings() {
    console.log('Initializing menu settings handler');

    // Handle model dropdown changes
    const modelInput = document.getElementById('model');
    const modelHidden = document.getElementById('model_hidden');

    if (modelHidden) {
        modelHidden.addEventListener('change', function(e) {
            console.log('Model changed to:', this.value);
            saveMenuSettings('llm.model', this.value);
        });
    }

    // Handle provider dropdown changes
    const providerSelect = document.getElementById('model_provider');
    if (providerSelect) {
        providerSelect.addEventListener('change', function(e) {
            console.log('Provider changed to:', this.value);
            saveMenuSettings('llm.provider', this.value);
        });
    }

    // Handle search engine dropdown changes
    const searchEngineHidden = document.getElementById('search_engine_hidden');
    if (searchEngineHidden) {
        searchEngineHidden.addEventListener('change', function(e) {
            console.log('Search engine changed to:', this.value);
            saveMenuSettings('search.tool', this.value);
        });
    }

    // Handle iterations and questions per iteration
    const iterationsInput = document.getElementById('iterations');
    if (iterationsInput) {
        iterationsInput.addEventListener('change', function(e) {
            console.log('Iterations changed to:', this.value);
            saveMenuSettings('search.iterations', this.value);
        });
    }

    const questionsInput = document.getElementById('questions_per_iteration');
    if (questionsInput) {
        questionsInput.addEventListener('change', function(e) {
            console.log('Questions per iteration changed to:', this.value);
            saveMenuSettings('search.questions_per_iteration', this.value);
        });
    }

    console.log('Menu settings handlers initialized');
}

// Call this function after the page and other scripts are loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit to ensure other scripts have loaded
    setTimeout(connectMenuSettings, 1000);
});
