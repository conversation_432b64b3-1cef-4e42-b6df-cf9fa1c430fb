/**
 * Library Status Component
 * Provides debugging tools for library loading issues
 */

window.libraryStatus = (function() {
    
    /**
     * Create a status panel showing library loading status
     * @returns {HTMLElement} Status panel element
     */
    function createStatusPanel() {
        const panel = document.createElement('div');
        panel.id = 'library-status-panel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            max-width: 400px;
            max-height: 300px;
            overflow-y: auto;
            display: none;
        `;
        
        updateStatusPanel(panel);
        return panel;
    }

    /**
     * Update the status panel content
     * @param {HTMLElement} panel - Status panel element
     */
    function updateStatusPanel(panel) {
        if (!window.libraryManager) {
            panel.innerHTML = '<div style="color: red;">❌ Library Manager not available</div>';
            return;
        }

        const status = window.libraryManager.getLibraryStatus();
        let html = '<div style="font-weight: bold; margin-bottom: 10px;">📚 Library Status</div>';
        
        Object.entries(status).forEach(([name, info]) => {
            const icon = info.available ? '✅' : (info.failed ? '❌' : '⏳');
            const color = info.available ? 'lightgreen' : (info.failed ? 'lightcoral' : 'yellow');
            const required = info.required ? ' (Required)' : ' (Optional)';
            
            html += `<div style="color: ${color}; margin: 5px 0;">
                ${icon} ${name}${required}
                ${info.loaded ? ' - Loaded' : ''}
                ${info.failed ? ' - Failed' : ''}
            </div>`;
        });

        // Add test buttons
        html += `
            <div style="margin-top: 15px; border-top: 1px solid #333; padding-top: 10px;">
                <button onclick="window.libraryStatus.testPdfGeneration()" style="margin: 2px; padding: 5px; font-size: 10px;">Test PDF</button>
                <button onclick="window.libraryStatus.testMarkdown()" style="margin: 2px; padding: 5px; font-size: 10px;">Test Markdown</button>
                <button onclick="window.libraryStatus.reloadLibraries()" style="margin: 2px; padding: 5px; font-size: 10px;">Reload Libs</button>
                <button onclick="window.libraryStatus.hidePanel()" style="margin: 2px; padding: 5px; font-size: 10px;">Hide</button>
            </div>
        `;

        panel.innerHTML = html;
    }

    /**
     * Show the status panel
     */
    function showStatusPanel() {
        let panel = document.getElementById('library-status-panel');
        if (!panel) {
            panel = createStatusPanel();
            document.body.appendChild(panel);
        }
        
        updateStatusPanel(panel);
        panel.style.display = 'block';
    }

    /**
     * Hide the status panel
     */
    function hideStatusPanel() {
        const panel = document.getElementById('library-status-panel');
        if (panel) {
            panel.style.display = 'none';
        }
    }

    /**
     * Test PDF generation
     */
    async function testPdfGeneration() {
        console.log('🧪 Testing PDF generation...');
        
        if (!window.pdfService) {
            console.error('❌ PDF service not available');
            alert('PDF service not available');
            return;
        }

        try {
            const result = await window.pdfService.testPdfGeneration();
            console.log('✅ PDF test result:', result);
            alert('PDF test completed - check console for details');
        } catch (error) {
            console.error('❌ PDF test failed:', error);
            alert('PDF test failed: ' + error.message);
        }
    }

    /**
     * Test markdown rendering
     */
    function testMarkdown() {
        console.log('🧪 Testing markdown rendering...');
        
        const testMarkdown = `# Test Markdown
        
## Features
- **Bold text**
- *Italic text*
- \`inline code\`

### Code Block
\`\`\`javascript
console.log('Hello, world!');
\`\`\`

[Link example](https://example.com)`;

        if (window.marked && typeof window.marked.parse === 'function') {
            try {
                const result = window.marked.parse(testMarkdown);
                console.log('✅ Marked test successful:', result.substring(0, 100) + '...');
                alert('Markdown test successful - check console for details');
            } catch (error) {
                console.error('❌ Marked test failed:', error);
                alert('Markdown test failed: ' + error.message);
            }
        } else {
            console.error('❌ Marked library not available');
            alert('Marked library not available');
        }
    }

    /**
     * Reload all libraries
     */
    async function reloadLibraries() {
        console.log('🔄 Reloading libraries...');
        
        if (!window.libraryManager) {
            alert('Library manager not available');
            return;
        }

        try {
            const results = await window.libraryManager.loadAllLibraries();
            console.log('✅ Library reload completed:', results);
            
            // Update status panel
            const panel = document.getElementById('library-status-panel');
            if (panel && panel.style.display !== 'none') {
                updateStatusPanel(panel);
            }
            
            alert(`Library reload completed:
✅ Loaded: ${results.loaded.join(', ') || 'none'}
❌ Failed: ${results.failed.join(', ') || 'none'}
⏭️ Skipped: ${results.skipped.join(', ') || 'none'}`);
        } catch (error) {
            console.error('❌ Library reload failed:', error);
            alert('Library reload failed: ' + error.message);
        }
    }

    /**
     * Add keyboard shortcut to show status panel
     */
    function initializeKeyboardShortcuts() {
        document.addEventListener('keydown', function(event) {
            // Ctrl+Shift+L to show library status
            if (event.ctrlKey && event.shiftKey && event.key === 'L') {
                event.preventDefault();
                showStatusPanel();
            }
        });
    }

    /**
     * Initialize the library status component
     */
    function initialize() {
        initializeKeyboardShortcuts();
        console.log('📊 Library Status component initialized. Press Ctrl+Shift+L to show status panel.');
    }

    // Auto-initialize when script loads
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // Public API
    return {
        showStatusPanel,
        hideStatusPanel,
        testPdfGeneration,
        testMarkdown,
        reloadLibraries,
        initialize
    };
})();
