/**
 * Detail Component
 * Manages the display of detailed information for a specific research topic
 */
(function() {
    // DOM Elements
    let detailContainer = null;
    let sourcesList = null;
    let tabsContainer = null;
    
    // Component state
    let currentResearchId = null;
    let currentTopicId = null;
    let detailData = null;
    
    /**
     * Initialize the detail component
     */
    function initializeDetail() {
        // Get IDs from URL
        const ids = getIdsFromUrl();
        currentResearchId = ids.researchId;
        currentTopicId = ids.topicId;
        
        if (!currentResearchId || !currentTopicId) {
            console.error('No research or topic ID found');
            if (window.ui && window.ui.showError) {
                window.ui.showError('Invalid research or topic. Please return to the results page.');
            }
            return;
        }
        
        // Get DOM elements
        detailContainer = document.getElementById('research-log');
        
        if (!detailContainer) {
            console.error('Required DOM elements not found for detail component');
            return;
        }
        
        // Set up event listeners
        setupEventListeners();
        
        // Load topic detail
        loadTopicDetail();
        
        console.log('Detail component initialized for research:', currentResearchId, 'topic:', currentTopicId);
    }
    
    /**
     * Extract research and topic IDs from URL
     * @returns {Object} Object with researchId and topicId
     */
    function getIdsFromUrl() {
        const pathParts = window.location.pathname.split('/');
        const detailIndex = pathParts.indexOf('detail');
        
        if (detailIndex > 0 && detailIndex + 2 < pathParts.length) {
            return {
                researchId: pathParts[detailIndex + 1],
                topicId: pathParts[detailIndex + 2]
            };
        }
        
        return { researchId: null, topicId: null };
    }
    
    /**
     * Set up event listeners
     */
    function setupEventListeners() {
        // Back button
        const backButton = document.getElementById('back-to-history-from-details');
        if (backButton) {
            backButton.addEventListener('click', function() {
                window.location.href = '/research/history';
            });
        }
        
        // Progress elements
        const progressBar = document.getElementById('detail-progress-fill');
        const progressPercentage = document.getElementById('detail-progress-percentage');
        
        // Tab click events
        if (tabsContainer) {
            tabsContainer.addEventListener('click', function(e) {
                if (e.target && e.target.matches('.tab-item')) {
                    const tabId = e.target.dataset.tab;
                    switchTab(tabId);
                }
            });
        }
        
        // Source highlight and citation copy
        if (sourcesList) {
            sourcesList.addEventListener('click', function(e) {
                // Handle citation copy
                if (e.target && e.target.matches('.copy-citation-btn')) {
                    const sourceId = e.target.closest('.source-item').dataset.id;
                    handleCopyCitation(sourceId);
                }
                
                // Handle source highlighting
                if (e.target && e.target.closest('.source-item')) {
                    const sourceItem = e.target.closest('.source-item');
                    if (!e.target.matches('.copy-citation-btn')) {
                        toggleSourceHighlight(sourceItem);
                    }
                }
            });
        }
    }
    
    /**
     * Load topic detail from API
     */
    async function loadTopicDetail() {
        // Show loading state
        window.ui.showSpinner(detailContainer, 'Loading topic details...');
        
        try {
            // Get topic detail
            detailData = await window.api.getTopicDetail(currentResearchId, currentTopicId);
            
            if (!detailData) {
                throw new Error('No topic details found');
            }
            
            // Render detail
            renderDetail(detailData);
        } catch (error) {
            console.error('Error loading topic detail:', error);
            window.ui.hideSpinner(detailContainer);
            window.ui.showError('Error loading topic detail: ' + error.message);
        }
    }
    
    /**
     * Render topic detail
     * @param {Object} data - The topic detail data
     */
    function renderDetail(data) {
        // Hide spinner
        window.ui.hideSpinner(detailContainer);
        
        // Set page title
        document.title = `${data.title || 'Topic Detail'} - Kitco Research AI`;
        
        // Update page header
        const pageTitle = document.querySelector('h1.page-title');
        if (pageTitle && data.title) {
            pageTitle.textContent = data.title;
        }
        
        // Render content
        if (data.content) {
            // Render markdown
            const renderedHtml = window.ui.renderMarkdown(data.content);
            detailContainer.innerHTML = renderedHtml;
            
            // Add syntax highlighting
            highlightCodeBlocks();
        } else {
            detailContainer.innerHTML = '<p class="text-error">No content available for this topic.</p>';
        }
        
        // Render sources
        if (sourcesList && data.sources && data.sources.length > 0) {
            renderSources(data.sources);
        }
        
        // Set sources count
        const sourcesCountEl = document.getElementById('sources-count');
        if (sourcesCountEl && data.sources) {
            sourcesCountEl.textContent = `${data.sources.length} source${data.sources.length !== 1 ? 's' : ''}`;
        }
        
        // Show first tab
        if (tabsContainer) {
            const firstTab = tabsContainer.querySelector('.tab-item');
            if (firstTab) {
                switchTab(firstTab.dataset.tab);
            }
        }
    }
    
    /**
     * Render sources list
     * @param {Array} sources - Array of source objects
     */
    function renderSources(sources) {
        sourcesList.innerHTML = '';
        
        sources.forEach((source, index) => {
            const sourceEl = document.createElement('div');
            sourceEl.className = 'source-item';
            sourceEl.dataset.id = index;
            
            const title = source.title || source.url || `Source ${index + 1}`;
            const url = source.url || '';
            const author = source.author || '';
            const date = source.date || '';
            
            sourceEl.innerHTML = `
                <div class="source-header">
                    <h4 class="source-title">${title}</h4>
                    <button class="copy-citation-btn" title="Copy citation">
                        <i class="fas fa-clipboard"></i>
                    </button>
                </div>
                ${url ? `<div class="source-url"><a href="${url}" target="_blank">${url}</a></div>` : ''}
                <div class="source-metadata">
                    ${author ? `<span><i class="fas fa-user"></i> ${author}</span>` : ''}
                    ${date ? `<span><i class="far fa-calendar"></i> ${date}</span>` : ''}
                </div>
                ${source.relevance ? `<div class="source-relevance">Relevance: ${source.relevance}</div>` : ''}
            `;
            
            sourcesList.appendChild(sourceEl);
        });
    }
    
    /**
     * Switch between tabs
     * @param {string} tabId - The tab ID to switch to
     */
    function switchTab(tabId) {
        // Update active tab
        const tabs = document.querySelectorAll('.tab-item');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.tab === tabId);
        });
        
        // Update visible content
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.style.display = content.dataset.tab === tabId ? 'block' : 'none';
        });
    }
    
    /**
     * Toggle source highlight
     * @param {HTMLElement} sourceEl - The source element to highlight
     */
    function toggleSourceHighlight(sourceEl) {
        // Remove highlight from all sources
        const allSources = document.querySelectorAll('.source-item');
        allSources.forEach(s => s.classList.remove('highlighted'));
        
        // Add highlight to clicked source
        sourceEl.classList.add('highlighted');
        
        // Get source index
        const sourceIndex = parseInt(sourceEl.dataset.id);
        
        // Highlight content with this source
        highlightContentFromSource(sourceIndex);
    }
    
    /**
     * Highlight content sections from a specific source
     * @param {number} sourceIndex - The source index to highlight
     */
    function highlightContentFromSource(sourceIndex) {
        // Remove all existing highlights
        const allCitations = document.querySelectorAll('.citation-highlight');
        allCitations.forEach(el => {
            el.classList.remove('citation-highlight');
        });
        
        // Add highlight to citations from this source
        const citations = document.querySelectorAll(`.citation[data-source="${sourceIndex}"]`);
        citations.forEach(citation => {
            citation.classList.add('citation-highlight');
            
            // Scroll to first citation
            if (citations.length > 0) {
                citations[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }
    
    /**
     * Handle copying citation
     * @param {number} sourceIndex - The source index to copy
     */
    async function handleCopyCitation(sourceIndex) {
        if (!detailData || !detailData.sources || !detailData.sources[sourceIndex]) {
            return;
        }
        
        const source = detailData.sources[sourceIndex];
        
        // Format citation
        let citation = '';
        
        if (source.author) {
            citation += source.author;
            if (source.date) {
                citation += ` (${source.date})`;
            }
            citation += '. ';
        }
        
        if (source.title) {
            citation += `"${source.title}". `;
        }
        
        if (source.url) {
            citation += `Retrieved from ${source.url}`;
        }
        
        // Copy to clipboard
        try {
            await navigator.clipboard.writeText(citation);
            
            // Show tooltip
            const btn = document.querySelector(`.source-item[data-id="${sourceIndex}"] .copy-citation-btn`);
            if (btn) {
                const originalHTML = btn.innerHTML;
                btn.innerHTML = '<i class="fas fa-check"></i>';
                
                setTimeout(() => {
                    btn.innerHTML = originalHTML;
                }, 2000);
            }
        } catch (error) {
            console.error('Failed to copy citation:', error);
        }
    }
    
    /**
     * Apply syntax highlighting to code blocks
     */
    function highlightCodeBlocks() {
        // Check if Prism is available
        if (typeof Prism !== 'undefined') {
            Prism.highlightAllUnder(detailContainer);
        }
    }
    
    // Initialize on DOM content loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeDetail);
    } else {
        initializeDetail();
    }
})(); 