"""
Custom exceptions for Kitco Research AI.

This module defines the exception hierarchy for the application,
providing specific error types for different failure scenarios.
"""

from typing import Any, Dict, Optional


class KRAError(Exception):
    """Base exception for all Kitco Research AI errors."""
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        return {
            "error": self.error_code,
            "message": self.message,
            "details": self.details
        }


class ConfigurationError(KRAError):
    """Raised when there are configuration-related errors."""
    pass


class DatabaseError(KRAError):
    """Raised when there are database-related errors."""
    pass


class SearchError(KRAError):
    """Raised when there are search-related errors."""
    pass


class ValidationError(KRAError):
    """Raised when input validation fails."""
    pass


class AuthenticationError(KRAError):
    """Raised when authentication fails."""
    pass


class AuthorizationError(KRAError):
    """Raised when authorization fails."""
    pass


class RateLimitError(KRAError):
    """Raised when rate limits are exceeded."""
    pass


class ExternalServiceError(KRAError):
    """Raised when external services fail."""
    pass


class ResourceNotFoundError(KRAError):
    """Raised when a requested resource is not found."""
    pass


class ResourceConflictError(KRAError):
    """Raised when there's a conflict with existing resources."""
    pass
