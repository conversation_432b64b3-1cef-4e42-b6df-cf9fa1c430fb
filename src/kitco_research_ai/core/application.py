"""
Application factory for Kitco Research AI.

This module provides the main application factory that sets up all components
with proper dependency injection and configuration.
"""

import os
from typing import Optional

from loguru import logger

from .container import Container, get_container
from .exceptions import ConfigurationError
from ..services.interfaces import (
    IConfigService,
    IDatabaseService,
    ISearchService,
    IReportService,
    ILoggingService
)


class Application:
    """
    Main application class that coordinates all services and components.
    
    This class serves as the entry point for the application and manages
    the lifecycle of all services through dependency injection.
    """
    
    def __init__(self, container: Optional[Container] = None):
        self.container = container or get_container()
        self._initialized = False
    
    def initialize(self, config_path: Optional[str] = None) -> None:
        """
        Initialize the application with all required services.
        
        Args:
            config_path: Optional path to configuration file
        """
        if self._initialized:
            logger.warning("Application already initialized")
            return
        
        try:
            logger.info("Initializing Kitco Research AI application")
            
            # Register core services
            self._register_core_services(config_path)
            
            # Register business services
            self._register_business_services()
            
            # Initialize services that need startup
            self._initialize_services()
            
            self._initialized = True
            logger.info("Application initialization completed successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize application: {e}")
            raise ConfigurationError(
                f"Application initialization failed: {e}",
                error_code="INIT_FAILED",
                details={"original_error": str(e)}
            )
    
    def _register_core_services(self, config_path: Optional[str]) -> None:
        """Register core infrastructure services."""
        from ..services.config_service import ConfigService
        from ..services.logging_service import LoggingService
        from ..services.database_service import DatabaseService
        
        # Configuration service (singleton)
        self.container.register_singleton(
            IConfigService,
            lambda: ConfigService(config_path)
        )
        
        # Logging service (singleton)
        self.container.register_singleton(
            ILoggingService,
            lambda: LoggingService(self.container.get(IConfigService))
        )
        
        # Database service (singleton)
        self.container.register_singleton(
            IDatabaseService,
            lambda: DatabaseService(self.container.get(IConfigService))
        )
    
    def _register_business_services(self) -> None:
        """Register business logic services."""
        from ..services.search_service import SearchService
        from ..services.report_service import ReportService
        
        # Search service (singleton)
        self.container.register_singleton(
            ISearchService,
            lambda: SearchService(
                self.container.get(IConfigService),
                self.container.get(IDatabaseService)
            )
        )
        
        # Report service (singleton)
        self.container.register_singleton(
            IReportService,
            lambda: ReportService(
                self.container.get(IConfigService),
                self.container.get(IDatabaseService)
            )
        )
    
    def _initialize_services(self) -> None:
        """Initialize services that need startup procedures."""
        # Initialize logging first
        logging_service = self.container.get(ILoggingService)
        logging_service.initialize()
        
        # Initialize database
        db_service = self.container.get(IDatabaseService)
        db_service.initialize()
        
        logger.info("Core services initialized successfully")
    
    def get_service(self, service_type):
        """Get a service from the container."""
        if not self._initialized:
            raise ConfigurationError(
                "Application not initialized. Call initialize() first.",
                error_code="APP_NOT_INITIALIZED"
            )
        return self.container.get(service_type)
    
    def shutdown(self) -> None:
        """Shutdown the application and cleanup resources."""
        if not self._initialized:
            return
        
        logger.info("Shutting down application")
        
        try:
            # Shutdown services in reverse order
            if self.container.has(IDatabaseService):
                db_service = self.container.get(IDatabaseService)
                db_service.shutdown()
            
            logger.info("Application shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during application shutdown: {e}")
        
        finally:
            self._initialized = False


def create_application(config_path: Optional[str] = None) -> Application:
    """
    Create and initialize a new application instance.
    
    Args:
        config_path: Optional path to configuration file
        
    Returns:
        Initialized Application instance
    """
    app = Application()
    app.initialize(config_path)
    return app
