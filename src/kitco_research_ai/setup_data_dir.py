#!/usr/bin/env python
"""
Data directory setup script for Kitco Research AI.
Creates the data directory for the application database if it doesn't exist.
"""

import os


def setup_data_dir():
    """Set up the data directory for the application."""
    # Get the project root directory (3 levels up from this file)
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))

    # Define the data directory path
    data_dir = os.path.join(project_root, "data")

    # Create the data directory if it doesn't exist
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
        print(f"Created data directory at: {data_dir}")
    else:
        print(f"Data directory already exists at: {data_dir}")

    # Return the path to the data directory
    return data_dir


if __name__ == "__main__":
    data_dir = setup_data_dir()
    db_path = os.path.join(data_dir, "ldr.db")
    print(f"Database path: {db_path}")
    print("Data directory setup completed successfully.")
