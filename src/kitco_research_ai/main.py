#!/usr/bin/env python
"""
Main CLI entry point for Kitco Research AI.

This module provides the command-line interface with improved error handling,
comprehensive health checks, and robust architecture integration.
"""

import argparse
import sys
from typing import Optional

from loguru import logger

from . import get_advanced_search_system
from .api.research_functions import analyze_documents, quick_summary
from .core.application import create_application
from .core.exceptions import KRAError
from .setup_data_dir import setup_data_dir


def main():
    """Main CLI entry point with improved error handling and architecture."""
    parser = argparse.ArgumentParser(
        description="Kitco Research AI - AI-powered research assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s research "climate change impacts" --strategy iterdrag
  %(prog)s summary "latest AI developments" --output summary.md
  %(prog)s web --port 8765 --host 0.0.0.0
  %(prog)s setup
  %(prog)s health --component database
        """
    )

    # Global options
    parser.add_argument(
        "--config", "-c",
        help="Configuration file path"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    parser.add_argument(
        "--quiet", "-q",
        action="store_true",
        help="Suppress non-error output"
    )

    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Research command
    research_parser = subparsers.add_parser(
        "research",
        help="Perform research on a topic",
        description="Conduct comprehensive research using AI and multiple search strategies"
    )
    research_parser.add_argument("query", help="Research query")
    research_parser.add_argument(
        "--strategy",
        choices=["standard", "iterdrag", "parallel", "rapid", "source_based"],
        default="iterdrag",
        help="Search strategy to use (default: iterdrag)"
    )
    research_parser.add_argument(
        "--output", "-o",
        help="Output file path"
    )
    research_parser.add_argument(
        "--max-iterations",
        type=int,
        help="Maximum number of search iterations"
    )
    research_parser.add_argument(
        "--questions-per-iteration",
        type=int,
        help="Number of search questions to generate per iteration"
    )

    # Quick summary command
    summary_parser = subparsers.add_parser(
        "summary",
        help="Generate quick summary",
        description="Generate a quick summary of a topic"
    )
    summary_parser.add_argument("query", help="Query for summary")
    summary_parser.add_argument(
        "--output", "-o",
        help="Output file path"
    )

    # Document analysis command
    docs_parser = subparsers.add_parser(
        "analyze",
        help="Analyze documents",
        description="Analyze documents in a collection"
    )
    docs_parser.add_argument("query", help="Analysis query")
    docs_parser.add_argument("collection", help="Document collection name")
    docs_parser.add_argument(
        "--output", "-o",
        help="Output file path"
    )

    # Setup command
    setup_parser = subparsers.add_parser(
        "setup",
        help="Setup data directory",
        description="Initialize the application data directory and database"
    )

    # Web command
    web_parser = subparsers.add_parser(
        "web",
        help="Start web interface",
        description="Start the web interface server"
    )
    web_parser.add_argument(
        "--port",
        type=int,
        default=8765,
        help="Port to run on (default: 8765)"
    )
    web_parser.add_argument(
        "--host",
        default="0.0.0.0",
        help="Host to bind to (default: 0.0.0.0)"
    )
    web_parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode"
    )

    # Health check command
    health_parser = subparsers.add_parser(
        "health",
        help="Check system health",
        description="Perform comprehensive health check of all components"
    )
    health_parser.add_argument(
        "--component",
        help="Check specific component only"
    )

    args = parser.parse_args()

    # Configure logging based on verbosity
    if args.quiet:
        logger.remove()
        logger.add(sys.stderr, level="ERROR")
    elif args.verbose:
        logger.remove()
        logger.add(sys.stderr, level="DEBUG", format="{time} | {level} | {name} | {message}")

    if not args.command:
        parser.print_help()
        return

    try:
        # Initialize application if needed (for commands that require it)
        app = None
        if args.command in ["health"]:
            app = create_application(args.config)

        if args.command == "setup":
            setup_data_dir()
            logger.info("Data directory setup completed")

        elif args.command == "web":
            from .web.app import main as web_main
            # Pass configuration and debug mode
            web_args = ["ldr-web", f"--port={args.port}", f"--host={args.host}"]
            if hasattr(args, 'debug') and args.debug:
                web_args.append("--debug")
            sys.argv = web_args
            web_main()

        elif args.command == "research":
            logger.info(f"Starting research on: {args.query}")

            # Create search system with additional parameters
            search_kwargs = {}
            if hasattr(args, 'max_iterations') and args.max_iterations:
                search_kwargs['max_iterations'] = args.max_iterations
            if hasattr(args, 'questions_per_iteration') and args.questions_per_iteration:
                search_kwargs['questions_per_iteration'] = args.questions_per_iteration

            system = get_advanced_search_system(strategy_name=args.strategy, **search_kwargs)
            results = system.analyze_topic(args.query)

            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(results.get('current_knowledge', 'No results found'))
                logger.info(f"Results saved to {args.output}")
            else:
                print(results.get('current_knowledge', 'No results found'))

        elif args.command == "summary":
            logger.info(f"Generating summary for: {args.query}")
            results = quick_summary(args.query)

            if args.output:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(results.get('summary', 'No summary generated'))
                logger.info(f"Summary saved to {args.output}")
            else:
                print(results.get('summary', 'No summary generated'))

        elif args.command == "analyze":
            logger.info(f"Analyzing documents in {args.collection} for: {args.query}")
            results = analyze_documents(args.query, args.collection, output_file=args.output)

            if not args.output:
                print(results.get('summary', 'No analysis results'))

        elif args.command == "health":
            from .services.interfaces import IHealthService
            health_service = app.get_service(IHealthService)

            if args.component:
                # Check specific component
                is_healthy = health_service.check_component(args.component)
                status = "✅ Healthy" if is_healthy else "❌ Unhealthy"
                print(f"{args.component}: {status}")
                sys.exit(0 if is_healthy else 1)
            else:
                # Comprehensive health check
                health_status = health_service.check_health()

                print(f"Overall Status: {health_status['overall_status'].upper()}")
                print(f"Timestamp: {health_status['timestamp']}")

                if 'components' in health_status:
                    print("\nComponent Status:")
                    for component, status in health_status['components'].items():
                        icon = "✅" if status['status'] == 'healthy' else "⚠️" if status['status'] == 'warning' else "❌"
                        print(f"  {icon} {component}: {status['status']} - {status['message']}")

                if 'metrics' in health_status:
                    print(f"\nCheck Duration: {health_status['metrics'].get('check_duration', 0):.2f}s")

                sys.exit(0 if health_status['overall_status'] == 'healthy' else 1)

    except KeyboardInterrupt:
        logger.info("Operation cancelled by user")
        sys.exit(1)
    except KRAError as e:
        logger.error(f"Application error: {e.message}")
        if args.verbose:
            logger.error(f"Error details: {e.details}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        if args.verbose:
            import traceback
            logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        # Cleanup application if created
        if app:
            app.shutdown()


if __name__ == "__main__":
    main()
