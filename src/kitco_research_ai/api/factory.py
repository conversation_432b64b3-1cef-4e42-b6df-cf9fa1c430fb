"""
Factory functions for creating Kitco Research AI components.

This module provides factory functions for creating search systems
and report generators with proper dependency injection and configuration.
"""

from typing import Optional

from loguru import logger

from ..core.application import create_application
from ..core.container import get_container
from ..core.exceptions import ConfigurationError
from ..services.interfaces import IConfigService, IDatabaseService
from ..search_system import AdvancedSearchSystem
from ..report_generator import IntegratedReportGenerator


def create_search_system(
    strategy_name: str = "iterdrag",
    config_path: Optional[str] = None,
    **kwargs
) -> AdvancedSearchSystem:
    """
    Create an advanced search system with proper dependency injection.
    
    Args:
        strategy_name: Search strategy to use
        config_path: Optional configuration file path
        **kwargs: Additional parameters for the search system
        
    Returns:
        Configured AdvancedSearchSystem instance
        
    Raises:
        ConfigurationError: If creation fails
    """
    try:
        logger.info(f"Creating search system with strategy: {strategy_name}")
        
        # Get or create application container
        container = get_container()
        
        # Initialize application if needed
        if not container.has(IConfigService):
            app = create_application(config_path)
            container = app.container
        
        # Get configuration service
        config_service = container.get(IConfigService)
        
        # Create search system with configuration
        search_config = config_service.get_search_config()
        llm_config = config_service.get_llm_config()
        
        # Override with any provided kwargs
        search_params = {
            "strategy_name": strategy_name,
            "include_text_content": kwargs.get("include_text_content", True),
            "use_cross_engine_filter": kwargs.get("use_cross_engine_filter", True),
            "max_iterations": kwargs.get("max_iterations"),
            "questions_per_iteration": kwargs.get("questions_per_iteration"),
        }
        
        # Remove None values
        search_params = {k: v for k, v in search_params.items() if v is not None}
        
        search_system = AdvancedSearchSystem(**search_params)
        
        logger.info(f"Search system created successfully with strategy: {strategy_name}")
        return search_system
        
    except Exception as e:
        logger.error(f"Failed to create search system: {e}")
        raise ConfigurationError(
            f"Failed to create search system: {e}",
            error_code="SEARCH_SYSTEM_CREATION_FAILED",
            details={"strategy": strategy_name, "error": str(e)}
        )


def create_report_generator(
    config_path: Optional[str] = None,
    **kwargs
) -> IntegratedReportGenerator:
    """
    Create a report generator with proper dependency injection.
    
    Args:
        config_path: Optional configuration file path
        **kwargs: Additional parameters for the report generator
        
    Returns:
        Configured IntegratedReportGenerator instance
        
    Raises:
        ConfigurationError: If creation fails
    """
    try:
        logger.info("Creating report generator")
        
        # Get or create application container
        container = get_container()
        
        # Initialize application if needed
        if not container.has(IConfigService):
            app = create_application(config_path)
            container = app.container
        
        # Get configuration service
        config_service = container.get(IConfigService)
        
        # Create report generator with configuration
        report_config = config_service.get("report", {})
        
        # Get LLM and search configurations
        llm_config = config_service.get_llm_config()
        search_config = config_service.get_search_config()
        
        # Create report generator
        # Note: IntegratedReportGenerator might need to be updated to accept these parameters
        report_generator = IntegratedReportGenerator()
        
        logger.info("Report generator created successfully")
        return report_generator
        
    except Exception as e:
        logger.error(f"Failed to create report generator: {e}")
        raise ConfigurationError(
            f"Failed to create report generator: {e}",
            error_code="REPORT_GENERATOR_CREATION_FAILED",
            details={"error": str(e)}
        )


def create_configured_llm(config_path: Optional[str] = None, **kwargs):
    """
    Create a configured LLM instance.
    
    Args:
        config_path: Optional configuration file path
        **kwargs: Override parameters
        
    Returns:
        Configured LLM instance
    """
    try:
        # Get or create application container
        container = get_container()
        
        # Initialize application if needed
        if not container.has(IConfigService):
            app = create_application(config_path)
            container = app.container
        
        # Get configuration service
        config_service = container.get(IConfigService)
        llm_config = config_service.get_llm_config()
        
        # Override with kwargs
        llm_config.update(kwargs)
        
        # Import and create LLM
        from ..config.llm_config import get_llm
        return get_llm(**llm_config)
        
    except Exception as e:
        logger.error(f"Failed to create LLM: {e}")
        raise ConfigurationError(
            f"Failed to create LLM: {e}",
            error_code="LLM_CREATION_FAILED",
            details={"error": str(e)}
        )


def create_configured_search_engine(config_path: Optional[str] = None, **kwargs):
    """
    Create a configured search engine instance.
    
    Args:
        config_path: Optional configuration file path
        **kwargs: Override parameters
        
    Returns:
        Configured search engine instance
    """
    try:
        # Get or create application container
        container = get_container()
        
        # Initialize application if needed
        if not container.has(IConfigService):
            app = create_application(config_path)
            container = app.container
        
        # Get configuration service
        config_service = container.get(IConfigService)
        search_config = config_service.get_search_config()
        
        # Override with kwargs
        search_config.update(kwargs)
        
        # Import and create search engine
        from ..config.search_config import get_search
        return get_search(**search_config)
        
    except Exception as e:
        logger.error(f"Failed to create search engine: {e}")
        raise ConfigurationError(
            f"Failed to create search engine: {e}",
            error_code="SEARCH_ENGINE_CREATION_FAILED",
            details={"error": str(e)}
        )
