"""
Search service implementation.

This service provides a clean interface to search operations
with proper error handling and validation.
"""

from typing import Any, Dict, List, Optional

from loguru import logger

from .interfaces import ISearchService, IConfigService, IDatabaseService
from ..core.exceptions import SearchError, ValidationError
from ..search_system import AdvancedSearchSystem


class SearchService(ISearchService):
    """
    Search service that manages search operations and engines.
    
    This service provides a clean interface to search functionality
    with proper validation, error handling, and configuration management.
    """
    
    def __init__(self, config_service: IConfigService, db_service: IDatabaseService):
        self.config_service = config_service
        self.db_service = db_service
        self._search_systems = {}
        logger.info("Search service initialized")
    
    def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """
        Perform a search with the specified parameters.
        
        Args:
            query: Search query
            **kwargs: Additional search parameters
            
        Returns:
            List of search results
            
        Raises:
            ValidationError: If query is invalid
            SearchError: If search fails
        """
        # Validate query
        if not self.validate_query(query):
            raise ValidationError(
                "Invalid search query",
                error_code="INVALID_SEARCH_QUERY",
                details={"query": query}
            )
        
        try:
            logger.info(f"Performing search for query: {query[:50]}...")
            
            # Get search parameters
            strategy = kwargs.get("strategy", "iterdrag")
            engine = kwargs.get("engine")
            max_results = kwargs.get("max_results")
            
            # Get or create search system
            search_system = self._get_search_system(strategy, **kwargs)
            
            # Perform search
            results = search_system.analyze_topic(query)
            
            # Extract search results from the analysis
            search_results = self._extract_search_results(results)
            
            logger.info(f"Search completed with {len(search_results)} results")
            return search_results
            
        except Exception as e:
            logger.error(f"Search failed for query '{query}': {e}")
            raise SearchError(
                f"Search operation failed: {e}",
                error_code="SEARCH_FAILED",
                details={"query": query, "error": str(e)}
            )
    
    def get_available_engines(self) -> List[str]:
        """
        Get list of available search engines.
        
        Returns:
            List of search engine names
        """
        try:
            # Import here to avoid circular imports
            from ..web_search_engines.search_engines_config import search_config
            config = search_config()
            return list(config.keys())
        except Exception as e:
            logger.error(f"Failed to get available engines: {e}")
            return ["searxng", "duckduckgo", "google", "brave"]  # Default fallback
    
    def validate_query(self, query: str) -> bool:
        """
        Validate a search query.
        
        Args:
            query: Search query to validate
            
        Returns:
            True if query is valid
        """
        if not query or not isinstance(query, str):
            return False
        
        # Check minimum length
        if len(query.strip()) < 2:
            return False
        
        # Check maximum length
        if len(query) > 1000:
            return False
        
        # Check for potentially harmful content
        harmful_patterns = ["<script", "javascript:", "data:"]
        query_lower = query.lower()
        for pattern in harmful_patterns:
            if pattern in query_lower:
                return False
        
        return True
    
    def _get_search_system(self, strategy: str, **kwargs) -> AdvancedSearchSystem:
        """Get or create a search system for the given strategy."""
        # Safe cache key creation that handles unhashable values
        try:
            import json
            # Convert kwargs to a JSON string for safe hashing
            kwargs_str = json.dumps(kwargs, sort_keys=True, default=str)
            cache_key = f"{strategy}_{hash(kwargs_str)}"
        except (TypeError, ValueError):
            # Fallback: use string representation
            cache_key = f"{strategy}_{hash(str(sorted(kwargs.items())))}"
        
        if cache_key not in self._search_systems:
            # Get search configuration
            search_config = self.config_service.get_search_config()
            
            # Create search system
            search_params = {
                "strategy_name": strategy,
                "include_text_content": kwargs.get("include_text_content", True),
                "use_cross_engine_filter": kwargs.get("use_cross_engine_filter", True),
                "max_iterations": kwargs.get("max_iterations"),
                "questions_per_iteration": kwargs.get("questions_per_iteration"),
            }
            
            # Remove None values
            search_params = {k: v for k, v in search_params.items() if v is not None}
            
            self._search_systems[cache_key] = AdvancedSearchSystem(**search_params)
        
        return self._search_systems[cache_key]
    
    def _extract_search_results(self, analysis_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract search results from analysis results."""
        search_results = []
        
        # Extract from current knowledge or findings
        if "findings" in analysis_results:
            findings = analysis_results["findings"]
            if isinstance(findings, list):
                for finding in findings:
                    if isinstance(finding, dict):
                        search_results.append({
                            "title": finding.get("title", ""),
                            "url": finding.get("url", ""),
                            "snippet": finding.get("content", ""),
                            "source": finding.get("source", ""),
                            "relevance_score": finding.get("relevance_score", 0.0)
                        })
        
        # Extract from sources if available
        if "sources" in analysis_results:
            sources = analysis_results["sources"]
            if isinstance(sources, list):
                for source in sources:
                    if isinstance(source, dict):
                        search_results.append({
                            "title": source.get("title", ""),
                            "url": source.get("url", ""),
                            "snippet": source.get("snippet", ""),
                            "source": source.get("engine", ""),
                            "relevance_score": 1.0
                        })
        
        return search_results
    
    def get_search_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent search history.
        
        Args:
            limit: Maximum number of entries to return
            
        Returns:
            List of search history entries
        """
        try:
            # Query database for search history
            query = """
                SELECT query, created_at, status, mode
                FROM research_history
                ORDER BY created_at DESC
                LIMIT :limit
            """
            
            results = self.db_service.execute_query(query, {"limit": limit})
            
            history = []
            for row in results:
                history.append({
                    "query": row[0],
                    "timestamp": row[1],
                    "status": row[2],
                    "mode": row[3]
                })
            
            return history
            
        except Exception as e:
            logger.error(f"Failed to get search history: {e}")
            return []
    
    def clear_cache(self) -> None:
        """Clear the search system cache."""
        self._search_systems.clear()
        logger.info("Search system cache cleared")
