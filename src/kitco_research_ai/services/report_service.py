"""
Report service implementation.

This service provides a clean interface to report generation
with proper error handling and validation.
"""

from typing import Any, Dict, Optional

from loguru import logger

from .interfaces import IReportService, IConfigService, IDatabaseService
from ..core.exceptions import ValidationError, DatabaseError
from ..report_generator import IntegratedReportGenerator


class ReportService(IReportService):
    """
    Report service that manages report generation and storage.
    
    This service provides a clean interface to report functionality
    with proper validation, error handling, and database integration.
    """
    
    def __init__(self, config_service: IConfigService, db_service: IDatabaseService):
        self.config_service = config_service
        self.db_service = db_service
        self._report_generator = None
        logger.info("Report service initialized")
    
    def generate_report(self, research_id: int, format: str = "markdown") -> str:
        """
        Generate a research report.
        
        Args:
            research_id: ID of the research to generate report for
            format: Report format (markdown, html, pdf)
            
        Returns:
            Generated report content
            
        Raises:
            ValidationError: If parameters are invalid
            DatabaseError: If research data cannot be retrieved
        """
        # Validate parameters
        if not isinstance(research_id, int) or research_id <= 0:
            raise ValidationError(
                "Invalid research ID",
                error_code="INVALID_RESEARCH_ID",
                details={"research_id": research_id}
            )
        
        if format not in ["markdown", "html", "pdf", "json"]:
            raise ValidationError(
                "Invalid report format",
                error_code="INVALID_REPORT_FORMAT",
                details={"format": format}
            )
        
        try:
            logger.info(f"Generating {format} report for research {research_id}")
            
            # Get research data
            research_data = self._get_research_data(research_id)
            if not research_data:
                raise DatabaseError(
                    f"Research {research_id} not found",
                    error_code="RESEARCH_NOT_FOUND",
                    details={"research_id": research_id}
                )
            
            # Get or create report generator
            report_generator = self._get_report_generator()
            
            # Generate report based on format
            if format == "markdown":
                content = self._generate_markdown_report(report_generator, research_data)
            elif format == "html":
                content = self._generate_html_report(report_generator, research_data)
            elif format == "pdf":
                content = self._generate_pdf_report(report_generator, research_data)
            elif format == "json":
                content = self._generate_json_report(research_data)
            else:
                content = self._generate_markdown_report(report_generator, research_data)
            
            # Save report to database
            self.save_report(research_id, content, {"format": format})
            
            logger.info(f"Report generated successfully for research {research_id}")
            return content
            
        except Exception as e:
            logger.error(f"Failed to generate report for research {research_id}: {e}")
            raise
    
    def get_report(self, research_id: int) -> Optional[Dict[str, Any]]:
        """
        Get an existing report.
        
        Args:
            research_id: ID of the research
            
        Returns:
            Report data or None if not found
        """
        try:
            query = """
                SELECT content, report_metadata, created_at, updated_at
                FROM research_report
                WHERE research_id = :research_id
            """
            
            results = self.db_service.execute_query(query, {"research_id": research_id})
            
            if results:
                row = results[0]
                return {
                    "content": row[0],
                    "metadata": row[1],
                    "created_at": row[2],
                    "updated_at": row[3]
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to get report for research {research_id}: {e}")
            return None
    
    def save_report(self, research_id: int, content: str, metadata: Optional[Dict] = None) -> bool:
        """
        Save a report to the database.
        
        Args:
            research_id: ID of the research
            content: Report content
            metadata: Optional metadata
            
        Returns:
            True if successful
        """
        try:
            # Check if report already exists
            existing_report = self.get_report(research_id)
            
            if existing_report:
                # Update existing report
                query = """
                    UPDATE research_report
                    SET content = :content, report_metadata = :metadata, updated_at = CURRENT_TIMESTAMP
                    WHERE research_id = :research_id
                """
                params = {"content": content, "metadata": metadata, "research_id": research_id}
            else:
                # Insert new report
                query = """
                    INSERT INTO research_report (research_id, content, report_metadata)
                    VALUES (:research_id, :content, :metadata)
                """
                params = {"research_id": research_id, "content": content, "metadata": metadata}
            
            self.db_service.execute_query(query, params)
            logger.info(f"Report saved for research {research_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save report for research {research_id}: {e}")
            return False
    
    def _get_research_data(self, research_id: int) -> Optional[Dict[str, Any]]:
        """Get research data from database."""
        try:
            # Get main research record
            query = """
                SELECT id, query, status, mode, created_at, updated_at,
                       start_time, end_time, duration, strategy
                FROM research_history
                WHERE id = :research_id
            """
            
            results = self.db_service.execute_query(query, {"research_id": research_id})
            
            if not results:
                return None
            
            row = results[0]
            research_data = {
                "id": row[0],
                "query": row[1],
                "status": row[2],
                "mode": row[3],
                "created_at": row[4],
                "updated_at": row[5],
                "start_time": row[6],
                "end_time": row[7],
                "duration": row[8],
                "strategy": row[9]
            }
            
            # Get research logs
            log_query = """
                SELECT message, level, timestamp, metadata
                FROM research_logs
                WHERE research_id = :research_id
                ORDER BY timestamp ASC
            """
            
            log_results = self.db_service.execute_query(log_query, {"research_id": research_id})
            research_data["logs"] = [
                {
                    "message": row[0],
                    "level": row[1], 
                    "timestamp": row[2],
                    "metadata": row[3]
                }
                for row in log_results
            ]
            
            # Get research resources
            resource_query = """
                SELECT url, title, content_type, relevance_score, metadata
                FROM research_resources
                WHERE research_id = :research_id
                ORDER BY relevance_score DESC
            """
            
            resource_results = self.db_service.execute_query(resource_query, {"research_id": research_id})
            research_data["resources"] = [
                {
                    "url": row[0],
                    "title": row[1],
                    "content_type": row[2],
                    "relevance_score": row[3],
                    "metadata": row[4]
                }
                for row in resource_results
            ]
            
            return research_data
            
        except Exception as e:
            logger.error(f"Failed to get research data for {research_id}: {e}")
            return None
    
    def _get_report_generator(self) -> IntegratedReportGenerator:
        """Get or create report generator instance."""
        if self._report_generator is None:
            self._report_generator = IntegratedReportGenerator()
        return self._report_generator
    
    def _generate_markdown_report(self, generator: IntegratedReportGenerator, data: Dict[str, Any]) -> str:
        """Generate markdown report."""
        # Use the existing report generator
        return generator.generate_markdown_report(data)
    
    def _generate_html_report(self, generator: IntegratedReportGenerator, data: Dict[str, Any]) -> str:
        """Generate HTML report."""
        # Convert markdown to HTML
        markdown_content = self._generate_markdown_report(generator, data)
        
        try:
            import markdown
            html_content = markdown.markdown(markdown_content, extensions=['tables', 'toc'])
            return f"""
<!DOCTYPE html>
<html>
<head>
    <title>Research Report - {data.get('query', 'Unknown')}</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; }}
        h1, h2, h3 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
{html_content}
</body>
</html>
"""
        except ImportError:
            logger.warning("Markdown library not available, returning plain markdown")
            return markdown_content
    
    def _generate_pdf_report(self, generator: IntegratedReportGenerator, data: Dict[str, Any]) -> str:
        """Generate PDF report (returns base64 encoded content)."""
        # This would require additional PDF generation libraries
        # For now, return HTML content
        return self._generate_html_report(generator, data)
    
    def _generate_json_report(self, data: Dict[str, Any]) -> str:
        """Generate JSON report."""
        import json
        return json.dumps(data, indent=2, default=str)
