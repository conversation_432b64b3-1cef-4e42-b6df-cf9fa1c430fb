"""
Validation service implementation.

This service provides comprehensive input validation and sanitization
for all user inputs and API requests.
"""

import re
import html
from typing import Any, Dict, List, Optional, Union

from loguru import logger

from .interfaces import IValidationService
from ..core.exceptions import ValidationError


class ValidationService(IValidationService):
    """
    Validation service that provides input validation and sanitization.
    
    This service ensures all user inputs are properly validated and sanitized
    to prevent security issues and data corruption.
    """
    
    def __init__(self):
        self._validation_schemas = self._load_validation_schemas()
        logger.info("Validation service initialized")
    
    def validate_input(self, data: Any, schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate input data against a schema.
        
        Args:
            data: Data to validate
            schema: Validation schema
            
        Returns:
            Validation result with errors and sanitized data
            
        Raises:
            ValidationError: If validation fails critically
        """
        try:
            result = {
                "valid": True,
                "errors": [],
                "warnings": [],
                "sanitized_data": data
            }
            
            # Validate based on schema type
            if schema.get("type") == "object":
                result = self._validate_object(data, schema, result)
            elif schema.get("type") == "string":
                result = self._validate_string(data, schema, result)
            elif schema.get("type") == "integer":
                result = self._validate_integer(data, schema, result)
            elif schema.get("type") == "number":
                result = self._validate_number(data, schema, result)
            elif schema.get("type") == "boolean":
                result = self._validate_boolean(data, schema, result)
            elif schema.get("type") == "array":
                result = self._validate_array(data, schema, result)
            else:
                result["errors"].append("Unknown schema type")
                result["valid"] = False
            
            return result
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            raise ValidationError(
                f"Validation error: {e}",
                error_code="VALIDATION_FAILED",
                details={"schema": schema, "data_type": type(data).__name__}
            )
    
    def sanitize_input(self, data: Any) -> Any:
        """
        Sanitize input data to prevent security issues.
        
        Args:
            data: Data to sanitize
            
        Returns:
            Sanitized data
        """
        if isinstance(data, str):
            return self._sanitize_string(data)
        elif isinstance(data, dict):
            return {k: self.sanitize_input(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self.sanitize_input(item) for item in data]
        else:
            return data
    
    def validate_search_query(self, query: str) -> Dict[str, Any]:
        """Validate a search query."""
        schema = {
            "type": "string",
            "min_length": 2,
            "max_length": 1000,
            "pattern": r"^[^<>\"'&]*$",  # No HTML/script injection
            "required": True
        }
        return self.validate_input(query, schema)
    
    def validate_research_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate research configuration."""
        schema = {
            "type": "object",
            "properties": {
                "strategy": {
                    "type": "string",
                    "enum": ["standard", "iterdrag", "parallel", "rapid", "source_based"]
                },
                "max_iterations": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 10
                },
                "max_results": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 100
                },
                "timeout": {
                    "type": "number",
                    "minimum": 10,
                    "maximum": 300
                }
            }
        }
        return self.validate_input(config, schema)
    
    def validate_llm_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Validate LLM configuration."""
        schema = {
            "type": "object",
            "properties": {
                "provider": {
                    "type": "string",
                    "enum": ["ollama", "openai", "google"]
                },
                "model": {
                    "type": "string",
                    "min_length": 1,
                    "max_length": 100
                },
                "temperature": {
                    "type": "number",
                    "minimum": 0.0,
                    "maximum": 2.0
                },
                "max_tokens": {
                    "type": "integer",
                    "minimum": 100,
                    "maximum": 100000
                }
            }
        }
        return self.validate_input(config, schema)
    
    def _validate_object(self, data: Any, schema: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate object data."""
        if not isinstance(data, dict):
            result["errors"].append("Expected object/dictionary")
            result["valid"] = False
            return result
        
        properties = schema.get("properties", {})
        required = schema.get("required", [])
        
        # Check required properties
        for prop in required:
            if prop not in data:
                result["errors"].append(f"Missing required property: {prop}")
                result["valid"] = False
        
        # Validate each property
        sanitized_data = {}
        for prop, value in data.items():
            if prop in properties:
                prop_result = self.validate_input(value, properties[prop])
                if not prop_result["valid"]:
                    result["errors"].extend([f"{prop}: {err}" for err in prop_result["errors"]])
                    result["valid"] = False
                sanitized_data[prop] = prop_result["sanitized_data"]
            else:
                # Unknown property - sanitize but allow
                sanitized_data[prop] = self.sanitize_input(value)
        
        result["sanitized_data"] = sanitized_data
        return result
    
    def _validate_string(self, data: Any, schema: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate string data."""
        if not isinstance(data, str):
            result["errors"].append("Expected string")
            result["valid"] = False
            return result
        
        # Check length constraints
        min_length = schema.get("min_length", 0)
        max_length = schema.get("max_length", float('inf'))
        
        if len(data) < min_length:
            result["errors"].append(f"String too short (minimum {min_length})")
            result["valid"] = False
        
        if len(data) > max_length:
            result["errors"].append(f"String too long (maximum {max_length})")
            result["valid"] = False
        
        # Check pattern
        pattern = schema.get("pattern")
        if pattern and not re.match(pattern, data):
            result["errors"].append("String does not match required pattern")
            result["valid"] = False
        
        # Check enum values
        enum_values = schema.get("enum")
        if enum_values and data not in enum_values:
            result["errors"].append(f"Value must be one of: {enum_values}")
            result["valid"] = False
        
        # Sanitize string
        result["sanitized_data"] = self._sanitize_string(data)
        return result
    
    def _validate_integer(self, data: Any, schema: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate integer data."""
        if not isinstance(data, int):
            try:
                data = int(data)
            except (ValueError, TypeError):
                result["errors"].append("Expected integer")
                result["valid"] = False
                return result
        
        # Check range constraints
        minimum = schema.get("minimum")
        maximum = schema.get("maximum")
        
        if minimum is not None and data < minimum:
            result["errors"].append(f"Value too small (minimum {minimum})")
            result["valid"] = False
        
        if maximum is not None and data > maximum:
            result["errors"].append(f"Value too large (maximum {maximum})")
            result["valid"] = False
        
        result["sanitized_data"] = data
        return result
    
    def _validate_number(self, data: Any, schema: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate number data."""
        if not isinstance(data, (int, float)):
            try:
                data = float(data)
            except (ValueError, TypeError):
                result["errors"].append("Expected number")
                result["valid"] = False
                return result
        
        # Check range constraints
        minimum = schema.get("minimum")
        maximum = schema.get("maximum")
        
        if minimum is not None and data < minimum:
            result["errors"].append(f"Value too small (minimum {minimum})")
            result["valid"] = False
        
        if maximum is not None and data > maximum:
            result["errors"].append(f"Value too large (maximum {maximum})")
            result["valid"] = False
        
        result["sanitized_data"] = data
        return result
    
    def _validate_boolean(self, data: Any, schema: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate boolean data."""
        if not isinstance(data, bool):
            if isinstance(data, str):
                if data.lower() in ["true", "1", "yes", "on"]:
                    data = True
                elif data.lower() in ["false", "0", "no", "off"]:
                    data = False
                else:
                    result["errors"].append("Expected boolean")
                    result["valid"] = False
                    return result
            else:
                result["errors"].append("Expected boolean")
                result["valid"] = False
                return result
        
        result["sanitized_data"] = data
        return result
    
    def _validate_array(self, data: Any, schema: Dict[str, Any], result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate array data."""
        if not isinstance(data, list):
            result["errors"].append("Expected array")
            result["valid"] = False
            return result
        
        # Check length constraints
        min_items = schema.get("min_items", 0)
        max_items = schema.get("max_items", float('inf'))
        
        if len(data) < min_items:
            result["errors"].append(f"Too few items (minimum {min_items})")
            result["valid"] = False
        
        if len(data) > max_items:
            result["errors"].append(f"Too many items (maximum {max_items})")
            result["valid"] = False
        
        # Validate items
        item_schema = schema.get("items")
        if item_schema:
            sanitized_items = []
            for i, item in enumerate(data):
                item_result = self.validate_input(item, item_schema)
                if not item_result["valid"]:
                    result["errors"].extend([f"Item {i}: {err}" for err in item_result["errors"]])
                    result["valid"] = False
                sanitized_items.append(item_result["sanitized_data"])
            result["sanitized_data"] = sanitized_items
        else:
            result["sanitized_data"] = [self.sanitize_input(item) for item in data]
        
        return result
    
    def _sanitize_string(self, text: str) -> str:
        """Sanitize a string to prevent XSS and injection attacks."""
        # HTML escape
        text = html.escape(text)
        
        # Remove potentially dangerous patterns
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'data:',
            r'vbscript:',
            r'on\w+\s*=',
        ]
        
        for pattern in dangerous_patterns:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE | re.DOTALL)
        
        return text.strip()
    
    def _load_validation_schemas(self) -> Dict[str, Dict[str, Any]]:
        """Load predefined validation schemas."""
        return {
            "search_query": {
                "type": "string",
                "min_length": 2,
                "max_length": 1000,
                "pattern": r"^[^<>\"'&]*$"
            },
            "port": {
                "type": "integer",
                "minimum": 1,
                "maximum": 65535
            },
            "temperature": {
                "type": "number",
                "minimum": 0.0,
                "maximum": 2.0
            }
        }
