"""
Logging service implementation.

This service provides centralized logging configuration and management
with support for multiple outputs and log levels.
"""

import sys
from typing import Any, Dict, List, Optional

from loguru import logger

from .interfaces import ILoggingService, IConfigService
from ..core.exceptions import ConfigurationError


class LoggingService(ILoggingService):
    """
    Logging service that manages application logging configuration.
    
    This service provides centralized logging management with support for
    multiple outputs, log rotation, and structured logging.
    """
    
    def __init__(self, config_service: IConfigService):
        self.config_service = config_service
        self._initialized = False
        self._log_handlers = []
    
    def initialize(self) -> None:
        """
        Initialize logging configuration.
        
        Raises:
            ConfigurationError: If logging initialization fails
        """
        if self._initialized:
            return
        
        try:
            logger.info("Initializing logging service")
            
            # Get logging configuration
            log_config = self.config_service.get_logging_config()
            
            # Remove default handler
            logger.remove()
            
            # Configure console logging
            self._configure_console_logging(log_config)
            
            # Configure file logging if specified
            if log_config.get("file"):
                self._configure_file_logging(log_config)
            
            self._initialized = True
            logger.info("Logging service initialized successfully")
            
        except Exception as e:
            # Use print since logger might not be configured yet
            print(f"Failed to initialize logging service: {e}")
            raise ConfigurationError(
                f"Logging initialization failed: {e}",
                error_code="LOGGING_INIT_FAILED"
            )
    
    def log(self, level: str, message: str, **kwargs) -> None:
        """
        Log a message with the specified level.
        
        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
            message: Log message
            **kwargs: Additional context data
        """
        if not self._initialized:
            return
        
        # Add context data to the log record
        with logger.contextualize(**kwargs):
            getattr(logger, level.lower())(message)
    
    def get_logs(self, level: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get recent logs.
        
        Args:
            level: Filter by log level
            limit: Maximum number of logs to return
            
        Returns:
            List of log entries
        """
        # This is a simplified implementation
        # In a production system, you might want to store logs in a database
        # or use a log aggregation service
        return []
    
    def _configure_console_logging(self, config: Dict[str, Any]) -> None:
        """Configure console logging output."""
        level = config.get("level", "INFO")
        format_string = config.get("format", "{time} | {level} | {message}")
        
        handler_id = logger.add(
            sys.stderr,
            level=level,
            format=format_string,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        self._log_handlers.append(handler_id)
        logger.debug("Console logging configured")
    
    def _configure_file_logging(self, config: Dict[str, Any]) -> None:
        """Configure file logging output."""
        file_path = config["file"]
        level = config.get("level", "INFO")
        format_string = config.get("format", "{time} | {level} | {message}")
        rotation = config.get("rotation", "1 day")
        retention = config.get("retention", "30 days")
        
        handler_id = logger.add(
            file_path,
            level=level,
            format=format_string,
            rotation=rotation,
            retention=retention,
            compression="gz",
            backtrace=True,
            diagnose=True
        )
        
        self._log_handlers.append(handler_id)
        logger.debug(f"File logging configured: {file_path}")
    
    def add_structured_logging(self, sink, **kwargs) -> int:
        """
        Add a structured logging sink.
        
        Args:
            sink: Logging sink (file, function, etc.)
            **kwargs: Additional configuration
            
        Returns:
            Handler ID
        """
        handler_id = logger.add(sink, **kwargs)
        self._log_handlers.append(handler_id)
        return handler_id
    
    def remove_handler(self, handler_id: int) -> None:
        """Remove a logging handler."""
        try:
            logger.remove(handler_id)
            if handler_id in self._log_handlers:
                self._log_handlers.remove(handler_id)
        except ValueError:
            logger.warning(f"Handler {handler_id} not found")
    
    def set_level(self, level: str) -> None:
        """
        Set the global logging level.
        
        Args:
            level: New logging level
        """
        # Remove existing handlers and reconfigure
        for handler_id in self._log_handlers:
            logger.remove(handler_id)
        
        self._log_handlers.clear()
        
        # Update configuration
        self.config_service.set("logging.level", level)
        
        # Reinitialize with new level
        self._initialized = False
        self.initialize()
    
    def get_current_level(self) -> str:
        """Get the current logging level."""
        return self.config_service.get("logging.level", "INFO")
    
    def create_logger(self, name: str, **kwargs) -> Any:
        """
        Create a named logger with specific configuration.
        
        Args:
            name: Logger name
            **kwargs: Logger configuration
            
        Returns:
            Configured logger instance
        """
        # For loguru, we can use contextualize or bind
        return logger.bind(logger_name=name, **kwargs)
