"""
Health monitoring service implementation.

This service provides comprehensive health checks for all application components
and monitors system resources and performance.
"""

import os
import time
from typing import Any, Dict, List, Optional

from loguru import logger

from .interfaces import IHealthService, IConfigService, IDatabaseService
from ..core.exceptions import ConfigurationError


class HealthService(IHealthService):
    """
    Health monitoring service that checks the status of all application components.
    
    This service provides comprehensive health monitoring including:
    - Database connectivity
    - Configuration validity
    - External service availability
    - System resource usage
    - Component-specific health checks
    """
    
    def __init__(self, config_service: IConfigService, db_service: IDatabaseService):
        self.config_service = config_service
        self.db_service = db_service
        self._last_check_time = None
        self._last_check_results = None
        logger.info("Health service initialized")
    
    def check_health(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check of all components.
        
        Returns:
            Dictionary containing health status of all components
        """
        start_time = time.time()
        
        try:
            logger.info("Starting comprehensive health check")
            
            health_status = {
                "timestamp": time.time(),
                "overall_status": "healthy",
                "components": {},
                "metrics": {},
                "errors": []
            }
            
            # Check core components
            health_status["components"]["database"] = self._check_database_health()
            health_status["components"]["configuration"] = self._check_configuration_health()
            health_status["components"]["filesystem"] = self._check_filesystem_health()
            health_status["components"]["memory"] = self._check_memory_health()
            health_status["components"]["search_engines"] = self._check_search_engines_health()
            health_status["components"]["llm"] = self._check_llm_health()
            
            # Calculate overall status
            component_statuses = [comp["status"] for comp in health_status["components"].values()]
            if "critical" in component_statuses:
                health_status["overall_status"] = "critical"
            elif "warning" in component_statuses:
                health_status["overall_status"] = "warning"
            
            # Add performance metrics
            health_status["metrics"]["check_duration"] = time.time() - start_time
            health_status["metrics"]["uptime"] = self._get_uptime()
            
            # Cache results
            self._last_check_time = time.time()
            self._last_check_results = health_status
            
            logger.info(f"Health check completed in {health_status['metrics']['check_duration']:.2f}s")
            return health_status
            
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {
                "timestamp": time.time(),
                "overall_status": "critical",
                "error": str(e),
                "components": {},
                "metrics": {"check_duration": time.time() - start_time}
            }
    
    def check_component(self, component: str) -> bool:
        """
        Check health of a specific component.
        
        Args:
            component: Component name to check
            
        Returns:
            True if component is healthy
        """
        try:
            if component == "database":
                return self._check_database_health()["status"] == "healthy"
            elif component == "configuration":
                return self._check_configuration_health()["status"] == "healthy"
            elif component == "filesystem":
                return self._check_filesystem_health()["status"] == "healthy"
            elif component == "memory":
                return self._check_memory_health()["status"] == "healthy"
            elif component == "search_engines":
                return self._check_search_engines_health()["status"] == "healthy"
            elif component == "llm":
                return self._check_llm_health()["status"] == "healthy"
            else:
                logger.warning(f"Unknown component for health check: {component}")
                return False
                
        except Exception as e:
            logger.error(f"Component health check failed for {component}: {e}")
            return False
    
    def _check_database_health(self) -> Dict[str, Any]:
        """Check database health."""
        try:
            # Test database connectivity
            is_healthy = self.db_service.health_check()
            
            if is_healthy:
                # Get database metrics
                db_config = self.config_service.get_database_config()
                db_path = db_config["path"]
                
                metrics = {}
                if os.path.exists(db_path):
                    stat = os.stat(db_path)
                    metrics["size_mb"] = stat.st_size / (1024 * 1024)
                    metrics["last_modified"] = stat.st_mtime
                
                return {
                    "status": "healthy",
                    "message": "Database is accessible",
                    "metrics": metrics
                }
            else:
                return {
                    "status": "critical",
                    "message": "Database is not accessible",
                    "metrics": {}
                }
                
        except Exception as e:
            return {
                "status": "critical",
                "message": f"Database health check failed: {e}",
                "metrics": {}
            }
    
    def _check_configuration_health(self) -> Dict[str, Any]:
        """Check configuration health."""
        try:
            # Test configuration access
            config = self.config_service.get_all()
            
            # Check for required configuration keys
            required_keys = [
                "app.port",
                "database.path",
                "llm.provider",
                "search.default_engine"
            ]
            
            missing_keys = []
            for key in required_keys:
                if self.config_service.get(key) is None:
                    missing_keys.append(key)
            
            if missing_keys:
                return {
                    "status": "warning",
                    "message": f"Missing configuration keys: {missing_keys}",
                    "metrics": {"total_keys": len(config), "missing_keys": len(missing_keys)}
                }
            else:
                return {
                    "status": "healthy",
                    "message": "Configuration is valid",
                    "metrics": {"total_keys": len(config)}
                }
                
        except Exception as e:
            return {
                "status": "critical",
                "message": f"Configuration health check failed: {e}",
                "metrics": {}
            }
    
    def _check_filesystem_health(self) -> Dict[str, Any]:
        """Check filesystem health."""
        try:
            # Check required directories
            required_dirs = ["data", "research_outputs"]
            missing_dirs = []
            
            for dir_name in required_dirs:
                if not os.path.exists(dir_name):
                    missing_dirs.append(dir_name)
            
            # Check disk space
            disk_usage = {}
            try:
                import shutil
                total, used, free = shutil.disk_usage(".")
                disk_usage = {
                    "total_gb": total / (1024**3),
                    "used_gb": used / (1024**3),
                    "free_gb": free / (1024**3),
                    "usage_percent": (used / total) * 100
                }
            except Exception:
                pass
            
            if missing_dirs:
                return {
                    "status": "warning",
                    "message": f"Missing directories: {missing_dirs}",
                    "metrics": disk_usage
                }
            elif disk_usage.get("usage_percent", 0) > 90:
                return {
                    "status": "warning",
                    "message": "Low disk space",
                    "metrics": disk_usage
                }
            else:
                return {
                    "status": "healthy",
                    "message": "Filesystem is healthy",
                    "metrics": disk_usage
                }
                
        except Exception as e:
            return {
                "status": "critical",
                "message": f"Filesystem health check failed: {e}",
                "metrics": {}
            }
    
    def _check_memory_health(self) -> Dict[str, Any]:
        """Check memory health."""
        try:
            import psutil
            
            memory = psutil.virtual_memory()
            metrics = {
                "total_gb": memory.total / (1024**3),
                "available_gb": memory.available / (1024**3),
                "used_gb": memory.used / (1024**3),
                "usage_percent": memory.percent
            }
            
            if memory.percent > 90:
                status = "critical"
                message = "Very high memory usage"
            elif memory.percent > 80:
                status = "warning"
                message = "High memory usage"
            else:
                status = "healthy"
                message = "Memory usage is normal"
            
            return {
                "status": status,
                "message": message,
                "metrics": metrics
            }
            
        except ImportError:
            return {
                "status": "warning",
                "message": "psutil not available for memory monitoring",
                "metrics": {}
            }
        except Exception as e:
            return {
                "status": "warning",
                "message": f"Memory health check failed: {e}",
                "metrics": {}
            }
    
    def _check_search_engines_health(self) -> Dict[str, Any]:
        """Check search engines health."""
        try:
            from ..web_search_engines.search_engines_config import search_config
            config = search_config()
            
            available_engines = list(config.keys())
            
            return {
                "status": "healthy",
                "message": f"Search engines available: {len(available_engines)}",
                "metrics": {"available_engines": available_engines}
            }
            
        except Exception as e:
            return {
                "status": "warning",
                "message": f"Search engines health check failed: {e}",
                "metrics": {}
            }
    
    def _check_llm_health(self) -> Dict[str, Any]:
        """Check LLM health."""
        try:
            llm_config = self.config_service.get_llm_config()
            provider = llm_config.get("provider", "unknown")
            model = llm_config.get("model", "unknown")
            
            # Basic configuration check
            if provider and model:
                return {
                    "status": "healthy",
                    "message": f"LLM configured: {provider}/{model}",
                    "metrics": {"provider": provider, "model": model}
                }
            else:
                return {
                    "status": "warning",
                    "message": "LLM configuration incomplete",
                    "metrics": {"provider": provider, "model": model}
                }
                
        except Exception as e:
            return {
                "status": "warning",
                "message": f"LLM health check failed: {e}",
                "metrics": {}
            }
    
    def _get_uptime(self) -> float:
        """Get application uptime in seconds."""
        # This is a simplified implementation
        # In a real application, you'd track the start time
        return time.time()
    
    def get_cached_health(self, max_age: int = 60) -> Optional[Dict[str, Any]]:
        """
        Get cached health results if they're recent enough.
        
        Args:
            max_age: Maximum age of cached results in seconds
            
        Returns:
            Cached health results or None
        """
        if (self._last_check_time and self._last_check_results and
            time.time() - self._last_check_time < max_age):
            return self._last_check_results
        return None
