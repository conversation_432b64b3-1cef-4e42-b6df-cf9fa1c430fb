"""
Service interfaces for Kitco Research AI.

This module defines the contracts for all services in the application,
promoting loose coupling and testability.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union


class IConfigService(ABC):
    """Interface for configuration management service."""
    
    @abstractmethod
    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any) -> bool:
        """Set a configuration value."""
        pass
    
    @abstractmethod
    def get_all(self) -> Dict[str, Any]:
        """Get all configuration values."""
        pass
    
    @abstractmethod
    def reload(self) -> None:
        """Reload configuration from sources."""
        pass


class IDatabaseService(ABC):
    """Interface for database management service."""
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize the database."""
        pass
    
    @abstractmethod
    def get_connection(self):
        """Get a database connection."""
        pass
    
    @abstractmethod
    def execute_query(self, query: str, params: Optional[Dict] = None) -> Any:
        """Execute a database query."""
        pass
    
    @abstractmethod
    def health_check(self) -> bool:
        """Check database health."""
        pass
    
    @abstractmethod
    def shutdown(self) -> None:
        """Shutdown database connections."""
        pass


class ISearchService(ABC):
    """Interface for search service."""
    
    @abstractmethod
    def search(self, query: str, **kwargs) -> List[Dict[str, Any]]:
        """Perform a search."""
        pass
    
    @abstractmethod
    def get_available_engines(self) -> List[str]:
        """Get list of available search engines."""
        pass
    
    @abstractmethod
    def validate_query(self, query: str) -> bool:
        """Validate a search query."""
        pass


class IReportService(ABC):
    """Interface for report generation service."""
    
    @abstractmethod
    def generate_report(self, research_id: int, format: str = "markdown") -> str:
        """Generate a research report."""
        pass
    
    @abstractmethod
    def get_report(self, research_id: int) -> Optional[Dict[str, Any]]:
        """Get an existing report."""
        pass
    
    @abstractmethod
    def save_report(self, research_id: int, content: str, metadata: Optional[Dict] = None) -> bool:
        """Save a report."""
        pass


class ILoggingService(ABC):
    """Interface for logging service."""
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize logging configuration."""
        pass
    
    @abstractmethod
    def log(self, level: str, message: str, **kwargs) -> None:
        """Log a message."""
        pass
    
    @abstractmethod
    def get_logs(self, level: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent logs."""
        pass


class IHealthService(ABC):
    """Interface for health monitoring service."""
    
    @abstractmethod
    def check_health(self) -> Dict[str, Any]:
        """Perform comprehensive health check."""
        pass
    
    @abstractmethod
    def check_component(self, component: str) -> bool:
        """Check health of specific component."""
        pass


class IValidationService(ABC):
    """Interface for validation service."""
    
    @abstractmethod
    def validate_input(self, data: Any, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Validate input data against schema."""
        pass
    
    @abstractmethod
    def sanitize_input(self, data: Any) -> Any:
        """Sanitize input data."""
        pass
