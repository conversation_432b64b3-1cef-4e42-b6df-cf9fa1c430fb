"""
Kitco Research AI - A tool for conducting deep research using AI.

This package provides a comprehensive AI-powered research assistant with:
- Comprehensive, iterative analysis using LLMs and web searches
- Multiple search strategies and engines
- Web interface and CLI tools
- Persistent research history and configuration
"""

__author__ = "HashedViking"
__description__ = "AI-powered research assistant with comprehensive, iterative analysis using LLMs and web searches"

from .__version__ import __version__

# Core application factory
from .core.application import create_application

# Public API
from .api.factory import create_search_system, create_report_generator

# Legacy compatibility
def get_llm(*args, **kwargs):
    """Legacy compatibility wrapper for LLM configuration."""
    from .config.llm_config import get_llm as _get_llm
    return _get_llm(*args, **kwargs)

def get_search(*args, **kwargs):
    """Legacy compatibility wrapper for search configuration."""
    from .config.search_config import get_search as _get_search
    return _get_search(*args, **kwargs)

def get_report_generator(*args, **kwargs):
    """Legacy compatibility wrapper for report generator."""
    from .report_generator import get_report_generator as _get_report_generator
    return _get_report_generator(*args, **kwargs)

def get_advanced_search_system(strategy_name: str = "iterdrag"):
    """
    Get an instance of the advanced search system.

    Args:
        strategy_name: The name of the search strategy to use

    Returns:
        AdvancedSearchSystem: An instance of the advanced search system
    """
    return create_search_system(strategy_name=strategy_name)

# Export main components
__all__ = [
    "__version__",
    "create_application",
    "create_search_system",
    "create_report_generator",
    "get_llm",
    "get_search",
    "get_report_generator",
    "get_advanced_search_system"
]
