# kitco_research_ai/config.py
from loguru import logger

from ..utilities.db_utils import get_db_setting
from ..web_search_engines.search_engine_factory import get_search as factory_get_search
from .llm_config import get_llm

# Whether to check the quality search results using the LLM.
QUALITY_CHECK_DDG_URLS = True
# Whether to only retrieve snippets instead of full search results.
SEARCH_SNIPPETS_ONLY = get_db_setting("search.snippets_only", True)


# Expose get_search function
def get_search(search_tool=None, llm_instance=None):
    """
    Helper function to get search engine

    Args:
        search_tool: Override the search tool setting (e.g. searxng, wikipedia)
        llm_instance: Override the LLM instance
    """

    # Use specified tool or default from settings
    tool = search_tool or get_db_setting("search.tool", "searxng")
    logger.info(f"Creating search engine with tool: {tool}")

    # Get LLM instance (use provided or get fresh one)
    llm = llm_instance or get_llm()

    # Get search parameters
    params = {
        "search_tool": tool,
        "llm_instance": llm,
        "max_results": get_db_setting("search.max_results", 10),
        "region": get_db_setting("search.region", "wt-wt"),
        "time_period": get_db_setting("search.time_period", "all"),
        "safe_search": get_db_setting("search.safe_search", True),
        "search_snippets_only": SEARCH_SNIPPETS_ONLY,
        "search_language": get_db_setting("search.search_language", "English"),
        "max_filtered_results": get_db_setting("search.max_filtered_results", 5),
    }

    # Log NULL parameters for debugging
    logger.info(
        f"Search config: tool={tool}, max_results={params['max_results']}, time_period={params['time_period']}"
    )

    # Create search engine
    search_engine = factory_get_search(**params)

    # Log the created engine type
    if search_engine:
        logger.info(
            f"Successfully created search engine of type: {type(search_engine).__name__}"
        )
    else:
        logger.error(f"Failed to create search engine for tool: {tool}")

    return search_engine
