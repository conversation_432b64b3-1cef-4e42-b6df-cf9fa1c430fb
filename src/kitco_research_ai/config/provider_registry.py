#!/usr/bin/env python3
"""
Unified Provider Registry - Single Source of Truth for LLM Providers
====================================================================

This module provides a centralized registry for all LLM providers, their configurations,
and capabilities. It serves as the single source of truth for provider management,
enabling easy maintenance, hot swapping, and dynamic provider discovery.

Features:
- Centralized provider configuration
- Dynamic provider discovery and validation
- Secure API key management
- Hot swapping capabilities
- Provider health checking
- Model discovery and caching
"""

import json
import os
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from loguru import logger
import requests

# Import secure secrets management
try:
    from config.secrets_manager import get_secret
    from config.environment import env_loader
    SECRETS_AVAILABLE = True
except ImportError:
    logger.warning("Secure secrets management not available, using environment variables only")
    SECRETS_AVAILABLE = False

# Import database utilities
try:
    from ..utilities.db_utils import get_db_setting
    DB_AVAILABLE = True
except ImportError:
    logger.warning("Database utilities not available")
    DB_AVAILABLE = False


@dataclass
class ProviderCapabilities:
    """Provider capabilities and limitations."""
    chat: bool = True
    streaming: bool = False
    function_calling: bool = False
    vision: bool = False
    max_tokens: int = 4000
    context_window: int = 4000


@dataclass
class ProviderConfig:
    """Complete provider configuration."""
    id: str
    name: str
    description: str
    type: str  # cloud, local, hybrid
    enabled: bool = True
    priority: int = 999
    hidden: bool = False
    api: Dict[str, Any] = field(default_factory=dict)
    models: Dict[str, Any] = field(default_factory=dict)
    capabilities: ProviderCapabilities = field(default_factory=ProviderCapabilities)
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # Runtime state
    available: bool = False
    last_checked: float = 0
    error_message: Optional[str] = None


class ProviderRegistry:
    """
    Centralized registry for LLM providers.
    
    Manages provider configurations, discovery, validation, and health checking.
    Provides a unified interface for all provider operations.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the provider registry.
        
        Args:
            config_path: Path to providers.json file
        """
        self.config_path = config_path or self._find_config_path()
        self.providers: Dict[str, ProviderConfig] = {}
        self.settings: Dict[str, Any] = {}
        self.validation_rules: Dict[str, Any] = {}
        self._cache_duration = 300  # 5 minutes
        self._last_loaded = 0
        
        self.load_providers()
    
    def _find_config_path(self) -> str:
        """Find the providers.json configuration file."""
        # Try multiple possible locations
        possible_paths = [
            Path(__file__).parent.parent.parent.parent / "config" / "providers.json",
            Path("config/providers.json"),
            Path("providers.json")
        ]
        
        for path in possible_paths:
            if path.exists():
                return str(path)
        
        raise FileNotFoundError("Could not find providers.json configuration file")
    
    def load_providers(self, force_reload: bool = False) -> None:
        """
        Load provider configurations from JSON file.
        
        Args:
            force_reload: Force reload even if cache is valid
        """
        current_time = time.time()
        
        # Check if we need to reload
        if not force_reload and (current_time - self._last_loaded) < self._cache_duration:
            return
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Load global settings
            self.settings = config_data.get('settings', {})
            self.validation_rules = config_data.get('validation', {})
            self._cache_duration = self.settings.get('cache_duration', 300)
            
            # Load providers
            self.providers.clear()
            providers_data = config_data.get('providers', {})
            
            for provider_id, provider_data in providers_data.items():
                try:
                    # Create capabilities object
                    caps_data = provider_data.get('capabilities', {})
                    capabilities = ProviderCapabilities(**caps_data)
                    
                    # Create provider config
                    provider_config = ProviderConfig(
                        id=provider_id,
                        name=provider_data.get('name', provider_id),
                        description=provider_data.get('description', ''),
                        type=provider_data.get('type', 'cloud'),
                        enabled=provider_data.get('enabled', True),
                        priority=provider_data.get('priority', 999),
                        hidden=provider_data.get('hidden', False),
                        api=provider_data.get('api', {}),
                        models=provider_data.get('models', {}),
                        capabilities=capabilities,
                        parameters=provider_data.get('parameters', {})
                    )
                    
                    self.providers[provider_id] = provider_config
                    logger.debug(f"Loaded provider: {provider_id}")
                    
                except Exception as e:
                    logger.error(f"Error loading provider {provider_id}: {e}")
                    continue
            
            self._last_loaded = current_time
            logger.info(f"Loaded {len(self.providers)} providers from {self.config_path}")
            
        except Exception as e:
            logger.error(f"Error loading providers configuration: {e}")
            raise
    
    def get_provider(self, provider_id: str) -> Optional[ProviderConfig]:
        """
        Get a specific provider configuration.
        
        Args:
            provider_id: Provider identifier
            
        Returns:
            Provider configuration or None if not found
        """
        self.load_providers()  # Ensure fresh data
        return self.providers.get(provider_id)
    
    def get_all_providers(self, include_hidden: bool = False, 
                         include_disabled: bool = False) -> Dict[str, ProviderConfig]:
        """
        Get all provider configurations.
        
        Args:
            include_hidden: Include hidden providers
            include_disabled: Include disabled providers
            
        Returns:
            Dictionary of provider configurations
        """
        self.load_providers()
        
        filtered_providers = {}
        for provider_id, provider in self.providers.items():
            if not include_hidden and provider.hidden:
                continue
            if not include_disabled and not provider.enabled:
                continue
            filtered_providers[provider_id] = provider
        
        return filtered_providers
    
    def get_available_providers(self, check_health: bool = True) -> Dict[str, ProviderConfig]:
        """
        Get all available (working) providers.
        
        Args:
            check_health: Perform health checks
            
        Returns:
            Dictionary of available provider configurations
        """
        providers = self.get_all_providers()
        
        if check_health:
            self.check_provider_health(list(providers.keys()))
        
        # Filter by availability
        available_providers = {
            pid: provider for pid, provider in providers.items()
            if provider.available
        }
        
        # Sort by priority
        sorted_providers = dict(sorted(
            available_providers.items(),
            key=lambda x: x[1].priority
        ))
        
        return sorted_providers
    
    def check_provider_health(self, provider_ids: Optional[List[str]] = None) -> Dict[str, bool]:
        """
        Check health status of providers.
        
        Args:
            provider_ids: List of provider IDs to check, or None for all
            
        Returns:
            Dictionary mapping provider IDs to health status
        """
        if provider_ids is None:
            provider_ids = list(self.providers.keys())
        
        health_status = {}
        timeout = self.settings.get('health_check_timeout', 3.0)
        
        for provider_id in provider_ids:
            provider = self.providers.get(provider_id)
            if not provider or not provider.enabled:
                health_status[provider_id] = False
                continue
            
            try:
                is_healthy = self._check_single_provider_health(provider, timeout)
                provider.available = is_healthy
                provider.last_checked = time.time()
                provider.error_message = None if is_healthy else "Health check failed"
                health_status[provider_id] = is_healthy
                
            except Exception as e:
                provider.available = False
                provider.error_message = str(e)
                health_status[provider_id] = False
                logger.debug(f"Health check failed for {provider_id}: {e}")
        
        return health_status
    
    def _check_single_provider_health(self, provider: ProviderConfig, timeout: float) -> bool:
        """Check health of a single provider."""
        if provider.type == "cloud":
            return self._check_cloud_provider_health(provider, timeout)
        elif provider.type == "local":
            return self._check_local_provider_health(provider, timeout)
        else:
            return False
    
    def _check_cloud_provider_health(self, provider: ProviderConfig, timeout: float) -> bool:
        """Check health of cloud provider by validating API key."""
        api_key = self.get_provider_api_key(provider.id)

        # Check if API key exists and is not a placeholder
        if not api_key:
            return False

        # Reject common placeholder values
        placeholder_values = [
            'sk-your-openai-api-key-here',
            'your-openai-api-key-here',
            'OPENAI_API_KEY',
            'CUSTOM_ENDPOINT',
            'your-api-key-here',
            'api-key-here'
        ]

        if api_key in placeholder_values:
            return False

        return True
    
    def _check_local_provider_health(self, provider: ProviderConfig, timeout: float) -> bool:
        """Check health of local provider by pinging endpoint."""
        base_url = self.get_provider_base_url(provider.id)
        if not base_url:
            return False
        
        health_endpoint = provider.api.get('health_check_endpoint', '/health')
        health_url = f"{base_url.rstrip('/')}{health_endpoint}"
        
        try:
            response = requests.get(health_url, timeout=timeout)
            return response.status_code == 200
        except Exception:
            return False


    def get_provider_api_key(self, provider_id: str) -> Optional[str]:
        """
        Get API key for a provider using secure resolution.

        Args:
            provider_id: Provider identifier

        Returns:
            API key or None if not found
        """
        provider = self.get_provider(provider_id)
        if not provider:
            return None

        api_config = provider.api

        # Try environment variable first
        env_key = api_config.get('api_key_env')
        if env_key:
            if SECRETS_AVAILABLE:
                api_key = get_secret(env_key)
                if api_key:
                    return api_key
            # Fallback to direct environment variable
            api_key = os.getenv(env_key)
            if api_key:
                return api_key

        # Try database setting
        db_key = api_config.get('api_key_db')
        if db_key and DB_AVAILABLE:
            api_key = get_db_setting(db_key)
            if api_key and api_key != db_key.upper():
                # If it looks like an env var reference, resolve it
                if api_key.isupper() and '_' in api_key:
                    if SECRETS_AVAILABLE:
                        resolved = get_secret(api_key)
                        if resolved and resolved != api_key:
                            return resolved
                    env_value = os.getenv(api_key)
                    if env_value:
                        return env_value
                else:
                    return api_key

        # Return placeholder if specified (for local providers)
        placeholder = api_config.get('api_key_placeholder')
        if placeholder:
            return placeholder

        return None

    def get_provider_base_url(self, provider_id: str) -> Optional[str]:
        """
        Get base URL for a provider using secure resolution.

        Args:
            provider_id: Provider identifier

        Returns:
            Base URL or None if not found
        """
        provider = self.get_provider(provider_id)
        if not provider:
            return None

        api_config = provider.api

        # Try environment variable first
        env_key = api_config.get('base_url_env')
        if env_key:
            url = os.getenv(env_key)
            if url:
                return url

        # Try database setting
        db_key = api_config.get('base_url_db')
        if db_key and DB_AVAILABLE:
            url = get_db_setting(db_key)
            if url:
                return url

        # Use default
        default_url = api_config.get('base_url_default') or api_config.get('base_url')
        return default_url

    def get_provider_models(self, provider_id: str, refresh: bool = False) -> List[str]:
        """
        Get available models for a provider.

        Args:
            provider_id: Provider identifier
            refresh: Force refresh from provider API

        Returns:
            List of available model names
        """
        provider = self.get_provider(provider_id)
        if not provider:
            return []

        models_config = provider.models

        # If dynamic discovery is enabled, try to fetch from API
        if models_config.get('dynamic_discovery', False) and (refresh or not models_config.get('available')):
            discovered_models = self._discover_provider_models(provider)
            if discovered_models:
                models_config['available'] = discovered_models

        # Return available models or default
        available_models = models_config.get('available', [])

    def is_provider_available(self, provider_id: str) -> bool:
        """
        Check if a provider is available at runtime.

        Args:
            provider_id: Provider identifier

        Returns:
            True if provider is available, False otherwise
        """
        provider = self.get_provider(provider_id)
        if not provider:
            return False

        try:
            # Check if provider has required configuration
            if provider_id.lower() == 'ollama':
                return self._check_ollama_availability()
            elif provider_id.lower() == 'openai':
                return self._check_openai_availability()
            elif provider_id.lower() == 'custom_endpoint':
                return self._check_custom_endpoint_availability()
            elif provider_id.lower() in ['vllm', 'lmstudio']:
                return self._check_local_api_availability(provider)
            else:
                # For other providers, check if they have basic configuration
                return bool(provider.api_key or provider.base_url)

        except Exception as e:
            logger.error(f"Error checking availability for provider {provider_id}: {e}")
            return False

    def _check_ollama_availability(self) -> bool:
        """Check if Ollama is available."""
        try:
            import os
            import requests

            # Check if Ollama base URL is configured
            base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')

            # Try to connect to Ollama API
            response = requests.get(f"{base_url}/api/tags", timeout=5)
            return response.status_code == 200

        except Exception:
            return False

    def _check_openai_availability(self) -> bool:
        """Check if OpenAI is available."""
        try:
            import os

            # Check if OpenAI API key is configured
            api_key = os.getenv('OPENAI_API_KEY')
            return bool(api_key and api_key.strip())

        except Exception:
            return False

    def _check_custom_endpoint_availability(self) -> bool:
        """Check if custom endpoint is available."""
        try:
            import os

            # Check if custom endpoint configuration exists
            api_key = os.getenv('CUSTOM_ENDPOINT_API_KEY')
            base_url = os.getenv('CUSTOM_ENDPOINT_BASE_URL')

            return bool(api_key and api_key.strip() and base_url and base_url.strip())

        except Exception:
            return False

    def _check_local_api_availability(self, provider) -> bool:
        """Check if local API provider is available."""
        try:
            import requests

            base_url = provider.base_url
            if not base_url:
                return False

            # Try to connect to the API
            response = requests.get(f"{base_url}/v1/models", timeout=5)
            return response.status_code == 200

        except Exception:
            return False
        if not available_models:
            default_model = models_config.get('default')
            if default_model:
                return [default_model]

        return available_models

    def _discover_provider_models(self, provider: ProviderConfig) -> List[str]:
        """Discover available models from provider API."""
        base_url = self.get_provider_base_url(provider.id)
        if not base_url:
            return []

        discovery_endpoint = provider.models.get('discovery_endpoint')
        if not discovery_endpoint:
            return []

        try:
            discovery_url = f"{base_url.rstrip('/')}{discovery_endpoint}"
            timeout = self.settings.get('health_check_timeout', 3.0)

            headers = {}
            api_key = self.get_provider_api_key(provider.id)
            if api_key and provider.api.get('api_key_required', True):
                headers['Authorization'] = f'Bearer {api_key}'

            response = requests.get(discovery_url, headers=headers, timeout=timeout)
            if response.status_code == 200:
                data = response.json()

                # Parse response based on provider type
                if provider.id == 'ollama':
                    # Ollama format: {"models": [{"name": "model1"}, ...]}
                    models = data.get('models', data)  # Handle both old and new formats
                    return [model.get('name', '') for model in models if model.get('name')]

                elif provider.id == 'lmstudio':
                    # LM Studio format: {"data": [{"id": "model1"}, ...]}
                    models = data.get('data', [])
                    return [model.get('id', '') for model in models if model.get('id')]

                else:
                    # Generic OpenAI format
                    models = data.get('data', [])
                    return [model.get('id', '') for model in models if model.get('id')]

        except Exception as e:
            logger.debug(f"Model discovery failed for {provider.id}: {e}")

        return []

    def get_default_provider(self) -> str:
        """Get the default provider ID."""
        return self.settings.get('default_provider', 'openai')

    def get_fallback_provider(self) -> str:
        """Get the fallback provider ID."""
        return self.settings.get('fallback_provider', 'openai')

    def validate_provider_config(self, provider_id: str) -> Tuple[bool, List[str]]:
        """
        Validate a provider configuration.

        Args:
            provider_id: Provider identifier

        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        provider = self.get_provider(provider_id)
        if not provider:
            return False, [f"Provider {provider_id} not found"]

        errors = []
        required_fields = self.validation_rules.get('required_fields', [])

        # Check required fields
        for field in required_fields:
            if not hasattr(provider, field) or not getattr(provider, field):
                errors.append(f"Missing required field: {field}")

        # Validate provider type
        valid_types = self.validation_rules.get('provider_types', ['cloud', 'local'])
        if provider.type not in valid_types:
            errors.append(f"Invalid provider type: {provider.type}")

        # Check API configuration
        if not provider.api:
            errors.append("Missing API configuration")

        return len(errors) == 0, errors


# Global registry instance
_registry_instance: Optional[ProviderRegistry] = None


def get_provider_registry() -> ProviderRegistry:
    """Get the global provider registry instance."""
    global _registry_instance
    if _registry_instance is None:
        _registry_instance = ProviderRegistry()
    return _registry_instance


def reload_provider_registry() -> ProviderRegistry:
    """Reload the provider registry from configuration."""
    global _registry_instance
    _registry_instance = ProviderRegistry()
    return _registry_instance
