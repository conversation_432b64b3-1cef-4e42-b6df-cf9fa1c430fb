Collecting sweetviz
  Downloading sweetviz-2.3.1-py3-none-any.whl.metadata (24 kB)
Requirement already satisfied: pandas!=1.0.0,!=1.0.1,!=1.0.2,>=0.25.3 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from sweetviz) (2.2.3)
Requirement already satisfied: numpy>=1.16.0 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from sweetviz) (2.1.3)
Requirement already satisfied: matplotlib>=3.1.3 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from sweetviz) (3.10.0)
Requirement already satisfied: tqdm>=4.43.0 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from sweetviz) (4.67.1)
Requirement already satisfied: scipy>=1.3.2 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from sweetviz) (1.15.3)
Requirement already satisfied: jinja2>=2.11.1 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from sweetviz) (3.1.6)
Requirement already satisfied: importlib-resources>=1.2.0 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from sweetviz) (6.5.2)
Requirement already satisfied: MarkupSafe>=2.0 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from jinja2>=2.11.1->sweetviz) (3.0.2)
Requirement already satisfied: contourpy>=1.0.1 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from matplotlib>=3.1.3->sweetviz) (1.3.2)
Requirement already satisfied: cycler>=0.10 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from matplotlib>=3.1.3->sweetviz) (0.12.1)
Requirement already satisfied: fonttools>=4.22.0 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from matplotlib>=3.1.3->sweetviz) (4.58.0)
Requirement already satisfied: kiwisolver>=1.3.1 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from matplotlib>=3.1.3->sweetviz) (1.4.8)
Requirement already satisfied: packaging>=20.0 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from matplotlib>=3.1.3->sweetviz) (24.2)
Requirement already satisfied: pillow>=8 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from matplotlib>=3.1.3->sweetviz) (11.2.1)
Requirement already satisfied: pyparsing>=2.3.1 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from matplotlib>=3.1.3->sweetviz) (3.2.3)
Requirement already satisfied: python-dateutil>=2.7 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from matplotlib>=3.1.3->sweetviz) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from pandas!=1.0.0,!=1.0.1,!=1.0.2,>=0.25.3->sweetviz) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from pandas!=1.0.0,!=1.0.1,!=1.0.2,>=0.25.3->sweetviz) (2025.2)
Requirement already satisfied: six>=1.5 in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from python-dateutil>=2.7->matplotlib>=3.1.3->sweetviz) (1.17.0)
Requirement already satisfied: colorama in c:\users\<USER>\documents\github\kitco-research-ai\.venv\lib\site-packages (from tqdm>=4.43.0->sweetviz) (0.4.6)
Downloading sweetviz-2.3.1-py3-none-any.whl (15.1 MB)
   ---------------------------------------- 15.1/15.1 MB 18.8 MB/s eta 0:00:00
Installing collected packages: sweetviz
Successfully installed sweetviz-2.3.1
