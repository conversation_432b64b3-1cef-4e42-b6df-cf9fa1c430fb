# 🔬 Kitco Research AI

AI-powered research assistant with deep, iterative analysis using LLMs and web searches.

## 🚀 Quick Start

### **🛡️ Bulletproof Setup** (Recommended)

**For a 100% reliable setup experience, see our [Bulletproof Setup Guide](docs/BULLETPROOF_SETUP_GUIDE.md)**

#### **Quick Bulletproof Setup:**

**Step 1: Pre-flight Check**
```bash
python scripts/setup/preflight_check.py
```

**Step 2: Robust Setup**
- **Windows**: `scripts\setup\setup.bat --development`
- **Linux/macOS**: `./scripts/setup/setup_robust.sh --development`
- **Cross-Platform**: `python scripts/setup/setup_cross_platform.py setup --robust --development`

**Step 3: Start Application**
- **Windows**: `scripts\start_app.bat`
- **Linux/macOS**: `./scripts/start_app.sh`
- **Cross-Platform**: `python scripts/setup/setup_cross_platform.py start`

### **Cross-Platform Setup** (Alternative)

#### **Windows** 🪟
```cmd
# Development Setup (Recommended)
setup.bat --development

# Start development server
start.bat

# Alternative: Use scripts directly
scripts\setup.bat --development
scripts\start_app.bat
```

#### **Linux/macOS** 🐧🍎
```bash
# Development Setup (Recommended)
./setup --development

# Start development server
./start

# Alternative: Use scripts directly
./scripts/setup.sh --development
./scripts/start_app.sh
```

#### **Python Cross-Platform Utility** 🐍
```bash
# Works on all platforms
python scripts/setup_cross_platform.py setup --development
python scripts/setup_cross_platform.py start
```

### **Environment Options**

#### **Development Setup** (Recommended)
- **Windows**: `setup.bat --development`
- **Linux/macOS**: `./setup --development`
- **Python**: `python scripts/setup_cross_platform.py setup --development`

Includes all dependencies plus development tools (testing, linting, debugging)

#### **Production Setup**
- **Windows**: `setup.bat --production`
- **Linux/macOS**: `./setup --production`
- **Python**: `python scripts/setup_cross_platform.py setup --production`

Minimal dependencies for production deployment

#### **Standard Setup**
- **Windows**: `setup.bat`
- **Linux/macOS**: `./setup`
- **Python**: `python scripts/setup_cross_platform.py setup`

All features without development tools

**Access the web interface**: http://localhost:8765

## 📁 Project Structure

```
kitco_research_ai/
├── docs/                   # 📚 Documentation
│   ├── setup/              # 🛠️ Setup guides and documentation
│   ├── development/        # 👨‍💻 Development documentation
│   └── reports/            # 📊 Historical reports and summaries
├── scripts/                # 🔧 Organized scripts
│   ├── setup/              # 🛠️ Setup and validation scripts
│   ├── docker/             # 🐳 Docker-related scripts
│   └── maintenance/        # 🔧 Maintenance and utility scripts
├── config/                 # ⚙️ Configuration files
├── src/                    # 💻 Source code
├── data/                   # 🗄️ Application data
├── app.py                  # 🎯 Main entry point
├── setup                   # 🛠️ Setup convenience script
├── start                   # ▶️ Start convenience script
└── restart                 # 🔄 Restart convenience script
```

## 📚 Documentation

### **Setup Guides**
- **[Bulletproof Setup Guide](docs/setup/BULLETPROOF_SETUP_GUIDE.md)** - 🛡️ **100% reliable setup (RECOMMENDED)**
- **[Python Version Compatibility](docs/setup/PYTHON_VERSION_COMPATIBILITY.md)** - 🐍 **Python 3.13+ compatibility guide**
- **[Cross-Platform Setup](docs/setup/CROSS_PLATFORM_SETUP.md)** - Comprehensive cross-platform guide
- **[Virtual Environment Guide](docs/setup/VENV_MIGRATION_GUIDE.md)** - Virtual environment setup
- **[Complete Documentation](docs/README.md)** - Full setup and usage guide
- **[Requirements Guide](config/README-requirements.md)** - Dependency management

### **Development Documentation**
- **[Architecture](docs/development/ARCHITECTURE.md)** - Technical architecture details
- **[OpenAI Integration](docs/development/OPENAI_INTEGRATION.md)** - API integration guide
- **[Branching Strategy](docs/development/BRANCHING_STRATEGY.md)** - Git workflow and branch management
- **[Quick Reference](docs/development/BRANCHING_QUICK_REFERENCE.md)** - Git branching cheat sheet

### **Operations Documentation**
- **[Production Guide](docs/PRODUCTION.md)** - Production deployment
- **[Security Guide](docs/SECURITY.md)** - Security configuration

## 🛠️ Available Commands

### **Cross-Platform Commands**

#### **Windows** 🪟
| Command | Purpose |
|---------|---------|
| `setup.bat [--env]` | Initial setup with environment options |
| `start.bat [port]` | Start application |
| `restart.bat [port]` | Fresh restart with cleanup |
| `scripts\setup.bat [--env]` | Direct setup script access |
| `scripts\start_app.bat [port]` | Direct start script access |
| `scripts\restart.bat [port]` | Direct restart script access |

#### **Linux/macOS** 🐧🍎
| Command | Purpose |
|---------|---------|
| `./setup [--env]` | Initial setup with environment options |
| `./start [port]` | Start application |
| `./restart [port]` | Fresh restart with cleanup |
| `./scripts/setup.sh [--env]` | Direct setup script access |
| `./scripts/start_app.sh [port]` | Direct start script access |
| `./scripts/restart.sh [port]` | Direct restart script access |
| `./scripts/status.sh` | Check project status |
| `./scripts/help.sh` | Show help and commands |

#### **Python Cross-Platform** 🐍
| Command | Purpose |
|---------|---------|
| `python scripts/setup_cross_platform.py setup [--env]` | Cross-platform setup |
| `python scripts/setup_cross_platform.py start [port]` | Cross-platform start |
| `python scripts/setup_cross_platform.py restart [port]` | Cross-platform restart |
| `python scripts/setup_cross_platform.py check` | Check prerequisites |

## 🐳 Docker Support

### **Quick Docker Start** (Recommended)
```bash
# Start development environment with SearXNG
./scripts/docker-start.sh

# Start production environment
./scripts/docker-start.sh -e prod

# Stop all containers
./scripts/docker-stop.sh

# Test containerized application
./scripts/docker-test.sh
```

### **Manual Docker Commands**
```bash
# Development with SearXNG
docker-compose --profile dev up -d

# Production with SearXNG
docker-compose --profile prod up -d

# Stop all services
docker-compose down

# Custom build
docker build -f Dockerfile -t kitco-research-ai .
```

### **Container Access**
- **Main Application**: http://localhost:8765
- **SearXNG Search**: http://localhost:8080
- **LM Studio Access**: Container can reach LM Studio on host via `host.docker.internal:1234`

### **Docker Features**
- ✅ **Integrated SearXNG** - Self-hosted search engine (required dependency)
- ✅ **Simplified setup** - Only core services (matches current project)
- ✅ **SQLite database** - Same as current setup (file-based)
- ✅ **Volume persistence** - Data survives container restarts
- ✅ **Complete portability** - Test anywhere Docker runs

## 🌳 Git Workflow

### **Branch Strategy**
We use GitFlow with 5 branch types:
- **`main`** - Production-ready code (always deployable)
- **`develop`** - Integration branch (latest features)
- **`feature/*`** - New features and bug fixes
- **`release/*`** - Release preparation
- **`hotfix/*`** - Emergency production fixes

### **Quick Start for Developers**
```bash
# Start new feature
./scripts/git/branch-helper.sh feature my-awesome-feature

# Start release
./scripts/git/branch-helper.sh release v1.2.0

# Emergency hotfix
./scripts/git/branch-helper.sh hotfix critical-bug

# Clean up merged branches
./scripts/git/branch-helper.sh cleanup
```

**📖 Full Documentation**: [Branching Strategy Guide](docs/development/BRANCHING_STRATEGY.md)

**🚀 Quick Reference**: [Branching Cheat Sheet](docs/development/BRANCHING_QUICK_REFERENCE.md)

## 🔧 Configuration

### **Cross-Platform Configuration**

1. **Environment Setup**:

   **Windows**:
   ```cmd
   copy src\kitco_research_ai\defaults\.env.template .env
   ```

   **Linux/macOS**:
   ```bash
   cp src/kitco_research_ai/defaults/.env.template .env
   ```

2. **Add API Keys**: Edit `.env` file with your OpenAI API key
3. **Choose Environment**: Use setup script with appropriate flags
4. **Start Application**: Run the start script

## 🆘 Need Help?

### **Platform-Specific Help**

**Windows** 🪟:
- Check [docs/README.md](docs/README.md) for detailed documentation
- View [troubleshooting guide](docs/README.md#-troubleshooting)

**Linux/macOS** 🐧🍎:
- Run `./scripts/help.sh` for quick reference
- Check [docs/README.md](docs/README.md) for detailed documentation
- View [troubleshooting guide](docs/README.md#-troubleshooting)

**All Platforms** 🌍:
- Use `python scripts/setup_cross_platform.py check` to verify prerequisites

---

### **Ready to start researching?** 🎉

**Windows**: Run `setup.bat` then `start.bat` and visit http://localhost:8765!

**Linux/macOS**: Run `./setup` then `./start` and visit http://localhost:8765!

**Cross-Platform**: Run `python scripts/setup_cross_platform.py setup` then `python scripts/setup_cross_platform.py start` and visit http://localhost:8765!
