name: Validate Setup Process

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'config/requirements*.txt'
      - 'scripts/setup/**'
      - '.github/workflows/validate-setup.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'config/requirements*.txt'
      - 'scripts/setup/**'
      - '.github/workflows/validate-setup.yml'
  schedule:
    # Run weekly to catch dependency drift
    - cron: '0 2 * * 1'

jobs:
  validate-dependencies:
    name: Validate Dependencies
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Validate dependency files
      run: |
        python scripts/setup/dependency_validator.py
    
    - name: Upload dependency report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: dependency-validation-report
        path: config/dependency_validation_report.txt

  test-setup-matrix:
    name: Test Setup
    needs: validate-dependencies
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.8', '3.9', '3.10', '3.11', '3.12']
        environment: [development, production, standard]
        exclude:
          # Reduce matrix size for efficiency
          - os: windows-latest
            python-version: '3.8'
          - os: windows-latest
            python-version: '3.9'
          - os: macos-latest
            python-version: '3.8'
          - os: macos-latest
            python-version: '3.9'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential
    
    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install --quiet
    
    - name: Run enhanced setup
      run: |
        python scripts/setup/setup_enhanced.py setup --environment ${{ matrix.environment }}
      timeout-minutes: 30
    
    - name: Validate installation
      run: |
        # Activate virtual environment and test imports
        if [ "$RUNNER_OS" == "Windows" ]; then
          .venv/Scripts/python -c "import flask, langchain, requests, pydantic; print('All imports successful')"
        else
          .venv/bin/python -c "import flask, langchain, requests, pydantic; print('All imports successful')"
        fi
      shell: bash
    
    - name: Test application startup
      run: |
        # Test that the application can start (dry run)
        if [ "$RUNNER_OS" == "Windows" ]; then
          timeout 30 .venv/Scripts/python -c "
            import sys
            sys.path.insert(0, '.')
            from src.kitco_research_ai.web.app_factory import create_app
            app = create_app()
            print('Application creation successful')
          " || true
        else
          timeout 30 .venv/bin/python -c "
            import sys
            sys.path.insert(0, '.')
            from src.kitco_research_ai.web.app_factory import create_app
            app = create_app()
            print('Application creation successful')
          " || true
        fi
      shell: bash
    
    - name: Upload setup logs
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: setup-logs-${{ matrix.os }}-py${{ matrix.python-version }}-${{ matrix.environment }}
        path: |
          *.log
          .venv/pip.log

  comprehensive-validation:
    name: Comprehensive Validation
    needs: test-setup-matrix
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Run comprehensive CI validation
      run: |
        python scripts/ci/validate_setup.py
    
    - name: Upload CI validation report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: ci-validation-report
        path: ci_validation_report.json

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.12'
    
    - name: Install pip-audit
      run: |
        python -m pip install --upgrade pip pip-audit
    
    - name: Audit dependencies
      run: |
        pip-audit --requirement config/requirements.txt --format=json --output=security-audit.json || true
        pip-audit --requirement config/requirements-dev.txt --format=json --output=security-audit-dev.json || true
    
    - name: Upload security audit
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-audit
        path: |
          security-audit.json
          security-audit-dev.json

  notify-on-failure:
    name: Notify on Failure
    needs: [validate-dependencies, test-setup-matrix, comprehensive-validation]
    runs-on: ubuntu-latest
    if: failure()
    
    steps:
    - name: Create Issue on Failure
      uses: actions/github-script@v6
      with:
        script: |
          const title = `Setup Validation Failed - ${new Date().toISOString().split('T')[0]}`;
          const body = `
          ## Setup Validation Failure
          
          The automated setup validation has failed. This indicates that users may experience setup issues.
          
          **Platform:** ${{ runner.os }}
          **Workflow:** ${{ github.workflow }}
          **Run ID:** ${{ github.run_id }}
          
          ### Action Required
          - [ ] Review the validation logs
          - [ ] Fix dependency conflicts
          - [ ] Update setup scripts if needed
          - [ ] Test setup manually on affected platforms
          
          ### Links
          - [Failed Workflow Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
          - [Setup Scripts](https://github.com/${{ github.repository }}/tree/main/scripts/setup)
          
          **Priority:** High - Setup issues affect all new users
          `;
          
          github.rest.issues.create({
            owner: context.repo.owner,
            repo: context.repo.repo,
            title: title,
            body: body,
            labels: ['bug', 'setup', 'high-priority']
          });
