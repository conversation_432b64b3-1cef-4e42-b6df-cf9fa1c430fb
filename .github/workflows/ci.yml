# ============================================================================
# GitHub Actions CI/CD Pipeline for Kitco Research AI
# ============================================================================
# Comprehensive CI/CD pipeline with testing, security, and deployment
# ============================================================================

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, release/* ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security audit weekly
    - cron: '0 2 * * 1'

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'

jobs:
  # ============================================================================
  # Code Quality and Testing
  # ============================================================================
  test:
    name: Test Suite
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.10', '3.11', '3.12']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('config/requirements-dev.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r config/requirements-dev.txt

    - name: Install Playwright browsers
      run: playwright install chromium

    - name: Run tests
      run: |
        pytest tests/ -v --cov=src/kitco_research_ai --cov-report=xml --cov-report=html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  # ============================================================================
  # Code Quality Checks
  # ============================================================================
  quality:
    name: Code Quality
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r config/requirements-dev.txt

    - name: Run Black (code formatting)
      run: black --check --diff src/ tests/

    - name: Run isort (import sorting)
      run: isort --check-only --diff src/ tests/

    - name: Run flake8 (linting)
      run: flake8 src/ tests/

    - name: Run mypy (type checking)
      run: mypy src/

    - name: Run pylint (static analysis)
      run: pylint src/kitco_research_ai/

  # ============================================================================
  # Security Scanning
  # ============================================================================
  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r config/requirements-dev.txt

    - name: Run pip-audit (dependency vulnerabilities)
      run: pip-audit --desc --format=json --output=audit-results.json

    - name: Run bandit (security linting)
      run: bandit -r src/ -f json -o bandit-results.json

    - name: Run safety (dependency safety)
      run: safety check --json --output safety-results.json

    - name: Run custom security check
      run: python scripts/security_check.py

    - name: Upload security results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-results
        path: |
          audit-results.json
          bandit-results.json
          safety-results.json

  # ============================================================================
  # Docker Build and Test
  # ============================================================================
  docker:
    name: Docker Build
    runs-on: ubuntu-latest
    needs: [test, quality]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build development image
      run: |
        docker build -f Dockerfile.dev -t kitco-research-ai:dev .

    - name: Build production image
      run: |
        docker build -f Dockerfile -t kitco-research-ai:prod .

    - name: Test production image
      run: |
        docker run --rm -d --name test-container -p 8765:8765 kitco-research-ai:prod
        sleep 30
        curl -f http://localhost:8765/health || exit 1
        docker stop test-container

  # ============================================================================
  # Deployment (only on main branch)
  # ============================================================================
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs: [test, quality, security, docker]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "🚀 Deploying to staging environment..."
        # Add your deployment commands here
        # Example: kubectl apply -f k8s/staging/
        
    - name: Run smoke tests
      run: |
        echo "🧪 Running smoke tests..."
        # Add smoke tests here
        
    - name: Deploy to production
      if: success()
      run: |
        echo "🚀 Deploying to production environment..."
        # Add your production deployment commands here
        # Example: kubectl apply -f k8s/production/

  # ============================================================================
  # Dependency Updates (scheduled)
  # ============================================================================
  dependency-update:
    name: Update Dependencies
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install pip-tools
      run: pip install pip-tools

    - name: Update requirements
      run: |
        pip-compile --upgrade config/requirements.in
        pip-compile --upgrade config/requirements-dev.in
        pip-compile --upgrade config/requirements-prod.in

    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: 'chore: update dependencies'
        title: 'Automated dependency updates'
        body: |
          This PR contains automated dependency updates.
          
          Please review the changes and ensure all tests pass before merging.
        branch: automated-dependency-updates
