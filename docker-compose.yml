# ============================================================================
# Kitco Research AI - Docker Compose Configuration
# ============================================================================
# Multi-environment Docker Compose setup for development and production
# Includes SearXNG integration for search functionality
# ============================================================================

services:
  # SearXNG Search Engine Service
  searxng:
    image: searxng/searxng:latest
    container_name: kitco-searxng
    ports:
      - "8080:8080"
    volumes:
      - searxng_data:/etc/searxng
    environment:
      - SEARXNG_BASE_URL=http://localhost:8080/
      - SEARXNG_SECRET_KEY=${SEARXNG_SECRET_KEY:-your-secret-key-here}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "sh", "-c", "curl -f --connect-timeout 3 --max-time 5 http://127.0.0.1:8080/ || exit 1"]
      interval: 15s
      timeout: 10s
      retries: 10
      start_period: 90s
    networks:
      - kitco-network

  # Development service
  kitco-research-ai-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: kitco-research-ai-dev
    ports:
      - "8765:8765"
    volumes:
      # Mount source code for hot-reload
      - .:/app
      # Persist data
      - ./data:/app/data
      - ./research_outputs:/app/research_outputs
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=development
      - DEBUG=True
      - PYTHONPATH=/app/src
      - SEARXNG_URL=http://searxng:8080
      - LM_STUDIO_URL=http://host.docker.internal:1234
    env_file:
      - .env
    depends_on:
      searxng:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:8765/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - kitco-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

  # Production service
  kitco-research-ai-prod:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: kitco-research-ai-prod
    ports:
      - "8765:8765"
    volumes:
      # Only persist data, not source code
      - ./data:/app/data
      - ./research_outputs:/app/research_outputs
      - ./logs:/app/logs
    environment:
      - FLASK_ENV=production
      - DEBUG=False
      - PYTHONPATH=/app/src
      - SEARXNG_URL=http://searxng:8080
      - LM_STUDIO_URL=http://host.docker.internal:1234
    env_file:
      - .env
    profiles:
      - prod
    depends_on:
      searxng:
        condition: service_started
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://127.0.0.1:8765/"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    networks:
      - kitco-network
    extra_hosts:
      - "host.docker.internal:host-gateway"

volumes:
  searxng_data:

networks:
  kitco-network:
    name: kitco-research-ai-network
    driver: bridge
