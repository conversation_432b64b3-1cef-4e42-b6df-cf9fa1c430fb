# Dynamic Provider and Model Loading Implementation

## Overview

This implementation adds comprehensive dynamic loading of model providers, language models, and search engines with proper button state management for the research form. The system now follows the requirements:

1. **Initial State**: Start Research button is disabled on load
2. **Dynamic Provider Loading**: Only available providers are shown
3. **Model Provider Selection Dependency**: Models load only after provider selection
4. **Dynamic Language Model Loading**: Models are fetched dynamically per provider
5. **Dynamic Search Engine Loading**: Only available search engines are shown
6. **Button Activation Logic**: Button enables only when all selections are valid

## Files Modified

### Backend Changes

1. **`src/kitco_research_ai/web/routes/api_routes.py`**
   - Added `/research/api/providers/health` endpoint
   - Added `/research/api/search-engines/health` endpoint
   - Provides runtime availability checking for providers and search engines

2. **`src/kitco_research_ai/config/provider_registry.py`**
   - Added `is_provider_available()` method
   - Added provider-specific availability checks for Ollama, OpenAI, Custom Endpoint, etc.
   - Implements runtime health checking

3. **`src/kitco_research_ai/web/routes/settings_routes.py`**
   - Enhanced `/research/settings/api/available-models` to only return available providers
   - Enhanced `/research/settings/api/available-search-engines` with runtime availability
   - Added SearXNG prioritization and fallback logic

### Frontend Changes

4. **`src/kitco_research_ai/web/static/js/components/research.js`**
   - Added comprehensive state management system (`appState`)
   - Implemented dynamic provider and search engine loading
   - Added form validation and button state management
   - Integrated with existing dropdown system for backward compatibility

5. **`src/kitco_research_ai/web/templates/pages/research.html`**
   - Set Start Research button to `disabled` by default

6. **`src/kitco_research_ai/web/static/css/styles.css`**
   - Enhanced disabled button styling for better visual feedback

## New API Endpoints

### Provider Health Check
```
GET /research/api/providers/health
```
Returns:
```json
{
  "success": true,
  "available_providers": [
    {"value": "OPENAI", "label": "OpenAI"},
    {"value": "OLLAMA", "label": "Ollama (Local)"}
  ],
  "provider_health": {
    "openai": {"available": true, "status": "healthy"},
    "ollama": {"available": false, "status": "unavailable"}
  }
}
```

### Search Engine Health Check
```
GET /research/api/search-engines/health
```
Returns:
```json
{
  "success": true,
  "available_engines": [
    {"value": "searxng", "label": "SearXNG"},
    {"value": "duckduckgo", "label": "DuckDuckGo"}
  ],
  "default_engine": "searxng",
  "searxng_available": true
}
```

## State Management

The frontend now uses a comprehensive state management system:

```javascript
let appState = {
  providers: {
    available: [],
    selected: null,
    loading: false
  },
  models: {
    available: [],
    selected: null,
    loading: false
  },
  searchEngines: {
    available: [],
    selected: null,
    defaultEngine: null,
    searxngAvailable: false,
    loading: false
  },
  validation: {
    providerSelected: false,
    modelSelected: false,
    searchEngineSelected: false,
    queryEntered: false
  },
  initialization: {
    complete: false,
    providersLoaded: false,
    searchEnginesLoaded: false
  }
};
```

## Button State Logic

The Start Research button is enabled only when:
- ✅ Query is entered
- ✅ Provider is selected
- ✅ Model is selected  
- ✅ Search engine is selected
- ✅ Initialization is complete

The button shows helpful tooltips when disabled, indicating what's missing.

## Fallback Logic

1. **SearXNG Priority**: If SearXNG is available, it's set as the default search engine
2. **Provider Fallback**: If a provider becomes unavailable, the user must select another
3. **Search Engine Fallback**: If selected engine becomes unavailable, automatically falls back to SearXNG if available
4. **Graceful Degradation**: System continues to work even if some dynamic loading fails

## Testing

Use the provided test script to verify the implementation:

```bash
python test_dynamic_loading.py
```

This will test all new endpoints and verify they return the expected data structure.

## Backward Compatibility

The implementation maintains full backward compatibility with existing code by:
- Keeping legacy variable names (`modelOptions`, `searchEngineOptions`)
- Preserving existing dropdown functionality
- Maintaining existing event handlers
- Using the same API response formats where possible

## Security Considerations

- All provider availability checks use secure environment variable access
- No hardcoded API keys or sensitive information
- Proper error handling prevents information leakage
- Input validation on all API endpoints

## Performance

- Parallel loading of providers and search engines
- Caching of availability checks
- Minimal DOM manipulation
- Efficient state updates
