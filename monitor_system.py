#!/usr/bin/env python3
"""
System monitoring script for the research application
"""

import requests
import json
import time
import datetime
import argparse
import sys

def check_system_health(base_url="http://localhost:5000"):
    """Check overall system health"""
    try:
        response = requests.get(f"{base_url}/research/api/system/health", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            return {"status": "error", "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"status": "error", "error": str(e)}

def check_providers(base_url="http://localhost:5000"):
    """Check provider availability"""
    try:
        response = requests.get(f"{base_url}/research/api/providers/health", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            return {"success": False, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def check_search_engines(base_url="http://localhost:5000"):
    """Check search engine availability"""
    try:
        response = requests.get(f"{base_url}/research/api/search-engines/health", timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            return {"success": False, "error": f"HTTP {response.status_code}"}
    except Exception as e:
        return {"success": False, "error": str(e)}

def format_status(status):
    """Format status with colors"""
    colors = {
        "healthy": "\033[92m✓\033[0m",  # Green
        "degraded": "\033[93m⚠\033[0m",  # Yellow
        "error": "\033[91m❌\033[0m",    # Red
        "unhealthy": "\033[91m❌\033[0m" # Red
    }
    return colors.get(status, status)

def print_health_report(health_data):
    """Print a formatted health report"""
    print(f"\n{'='*60}")
    print(f"SYSTEM HEALTH REPORT - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    overall_status = health_data.get("status", "unknown")
    print(f"Overall Status: {format_status(overall_status)} {overall_status.upper()}")
    
    if "components" in health_data:
        print(f"\nComponent Status:")
        for component, details in health_data["components"].items():
            status = details.get("status", "unknown")
            print(f"  {component.title()}: {format_status(status)} {status}")
            
            if details.get("fallback_mode"):
                print(f"    ⚠️  Running in fallback mode")
            
            if "available_count" in details:
                print(f"    Available: {details['available_count']}")
            
            if "error" in details:
                print(f"    Error: {details['error']}")
    
    if "summary" in health_data:
        summary = health_data["summary"]
        print(f"\nSummary:")
        print(f"  Total Components: {summary.get('total_components', 0)}")
        print(f"  Healthy: {summary.get('healthy_components', 0)}")
        print(f"  Degraded: {summary.get('degraded_components', 0)}")
        print(f"  Errors: {summary.get('error_components', 0)}")

def print_providers_report(providers_data):
    """Print providers status report"""
    print(f"\n{'='*60}")
    print("PROVIDERS REPORT")
    print(f"{'='*60}")
    
    if providers_data.get("success"):
        providers = providers_data.get("available_providers", [])
        print(f"Available Providers: {len(providers)}")
        
        for provider in providers:
            print(f"  ✓ {provider.get('label', 'Unknown')} ({provider.get('value', 'Unknown')})")
        
        if providers_data.get("fallback_mode"):
            print("  ⚠️  Running in fallback mode")
    else:
        print(f"❌ Error: {providers_data.get('error', 'Unknown error')}")

def print_search_engines_report(engines_data):
    """Print search engines status report"""
    print(f"\n{'='*60}")
    print("SEARCH ENGINES REPORT")
    print(f"{'='*60}")
    
    if engines_data.get("success"):
        engines = engines_data.get("available_engines", [])
        print(f"Available Engines: {len(engines)}")
        
        for engine in engines:
            print(f"  ✓ {engine.get('label', 'Unknown')} ({engine.get('value', 'Unknown')})")
        
        if engines_data.get("searxng_available"):
            print("  ✓ SearXNG is available (preferred)")
        
        default_engine = engines_data.get("default_engine")
        if default_engine:
            print(f"  Default Engine: {default_engine}")
        
        if engines_data.get("fallback_mode"):
            print("  ⚠️  Running in fallback mode")
    else:
        print(f"❌ Error: {engines_data.get('error', 'Unknown error')}")

def monitor_continuous(base_url, interval=30):
    """Continuously monitor the system"""
    print(f"Starting continuous monitoring (interval: {interval}s)")
    print("Press Ctrl+C to stop")
    
    try:
        while True:
            health_data = check_system_health(base_url)
            
            # Clear screen (works on most terminals)
            print("\033[2J\033[H")
            
            print_health_report(health_data)
            
            # Show next check time
            next_check = datetime.datetime.now() + datetime.timedelta(seconds=interval)
            print(f"\nNext check: {next_check.strftime('%H:%M:%S')}")
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n\nMonitoring stopped.")

def main():
    parser = argparse.ArgumentParser(description="Monitor research application health")
    parser.add_argument("--url", default="http://localhost:5000", help="Base URL of the application")
    parser.add_argument("--continuous", "-c", action="store_true", help="Continuous monitoring mode")
    parser.add_argument("--interval", "-i", type=int, default=30, help="Monitoring interval in seconds")
    parser.add_argument("--providers", "-p", action="store_true", help="Show detailed providers report")
    parser.add_argument("--engines", "-e", action="store_true", help="Show detailed search engines report")
    parser.add_argument("--json", "-j", action="store_true", help="Output raw JSON")
    
    args = parser.parse_args()
    
    if args.continuous:
        monitor_continuous(args.url, args.interval)
        return
    
    # Single check mode
    health_data = check_system_health(args.url)
    
    if args.json:
        print(json.dumps(health_data, indent=2))
        return
    
    print_health_report(health_data)
    
    if args.providers:
        providers_data = check_providers(args.url)
        print_providers_report(providers_data)
    
    if args.engines:
        engines_data = check_search_engines(args.url)
        print_search_engines_report(engines_data)
    
    # Exit with appropriate code
    status = health_data.get("status", "error")
    if status == "healthy":
        sys.exit(0)
    elif status == "degraded":
        sys.exit(1)
    else:
        sys.exit(2)

if __name__ == "__main__":
    main()
