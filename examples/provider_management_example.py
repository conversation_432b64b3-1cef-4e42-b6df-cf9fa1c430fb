#!/usr/bin/env python3
"""
Provider Management Example
==========================

This example demonstrates how to use the Unified Provider System for managing
LLM providers, configurations, and hot swapping in Kitco Research AI.

Run this example to see the provider system in action.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.kitco_research_ai.config.provider_registry import get_provider_registry
from src.kitco_research_ai.web.services.provider_service import get_provider_service


def demonstrate_provider_registry():
    """Demonstrate basic provider registry operations."""
    print("🔍 Provider Registry Demo")
    print("=" * 50)
    
    # Get the registry instance
    registry = get_provider_registry()
    
    # List all providers
    print("\n📋 All Available Providers:")
    providers = registry.get_available_providers(check_health=True)
    
    for provider_id, provider_config in providers.items():
        status = "✅" if provider_config.available else "❌"
        print(f"  {status} {provider_config.name} ({provider_id})")
        print(f"     Type: {provider_config.type}")
        print(f"     Description: {provider_config.description}")
        
        # Show API key status (without revealing the key)
        api_key = registry.get_provider_api_key(provider_id)
        key_status = "🔑 Configured" if api_key else "🚫 Missing"
        print(f"     API Key: {key_status}")
        
        # Show available models (first 3)
        models = registry.get_provider_models(provider_id)
        if models:
            model_list = ", ".join(models[:3])
            if len(models) > 3:
                model_list += f" (+{len(models)-3} more)"
            print(f"     Models: {model_list}")
        
        print()


def demonstrate_provider_service():
    """Demonstrate provider service operations."""
    print("\n🛠️ Provider Service Demo")
    print("=" * 50)
    
    # Get the service instance
    service = get_provider_service()
    
    # Show current configuration
    current_provider = service.get_current_provider()
    current_model = service.get_current_model()
    
    print(f"\n🎯 Current Configuration:")
    print(f"   Provider: {current_provider}")
    print(f"   Model: {current_model}")
    
    # Get UI-friendly provider list
    print(f"\n📱 UI Provider Information:")
    ui_providers = service.get_available_providers_for_ui()
    
    for provider_id, info in ui_providers.items():
        status = "✅ Available" if info['available'] else "❌ Unavailable"
        print(f"   {info['name']}: {status}")
        if info['error_message']:
            print(f"      Error: {info['error_message']}")


def demonstrate_hot_swapping():
    """Demonstrate hot swapping capabilities."""
    print("\n🔄 Hot Swapping Demo")
    print("=" * 50)
    
    service = get_provider_service()
    registry = get_provider_registry()
    
    # Get available providers
    available_providers = registry.get_available_providers()
    
    if len(available_providers) < 2:
        print("⚠️ Need at least 2 available providers to demonstrate hot swapping")
        return
    
    # Get current provider
    original_provider = service.get_current_provider()
    print(f"🎯 Current provider: {original_provider}")
    
    # Find a different available provider
    target_provider = None
    for provider_id in available_providers:
        if provider_id != original_provider:
            target_provider = provider_id
            break
    
    if not target_provider:
        print("⚠️ No alternative provider available for hot swapping")
        return
    
    print(f"🔄 Attempting to switch to: {target_provider}")
    
    # Attempt hot swap
    success, message = service.set_current_provider(target_provider)
    
    if success:
        print(f"✅ {message}")
        
        # Verify the switch
        new_provider = service.get_current_provider()
        print(f"🎯 New current provider: {new_provider}")
        
        # Show available models for new provider
        models = registry.get_provider_models(target_provider)
        if models:
            print(f"📚 Available models: {', '.join(models[:3])}")
        
        # Switch back to original provider
        print(f"\n🔄 Switching back to: {original_provider}")
        success, message = service.set_current_provider(original_provider)
        if success:
            print(f"✅ {message}")
        else:
            print(f"❌ Failed to switch back: {message}")
    else:
        print(f"❌ Hot swap failed: {message}")


def demonstrate_model_discovery():
    """Demonstrate dynamic model discovery."""
    print("\n🔍 Model Discovery Demo")
    print("=" * 50)
    
    registry = get_provider_registry()
    service = get_provider_service()
    
    # Find providers with dynamic discovery
    providers = registry.get_all_providers()
    
    for provider_id, provider_config in providers.items():
        if provider_config.models.get('dynamic_discovery', False):
            print(f"\n📡 Discovering models for {provider_config.name}:")
            
            # Refresh models from provider
            success, models = service.refresh_provider_models(provider_id)
            
            if success and models:
                print(f"   Found {len(models)} models:")
                for i, model in enumerate(models[:5], 1):
                    print(f"   {i:2d}. {model}")
                if len(models) > 5:
                    print(f"       ... and {len(models)-5} more")
            elif success:
                print("   No models found")
            else:
                print("   ❌ Discovery failed")


def demonstrate_validation():
    """Demonstrate provider validation."""
    print("\n✅ Provider Validation Demo")
    print("=" * 50)
    
    service = get_provider_service()
    registry = get_provider_registry()
    
    # Validate all providers
    providers = registry.get_all_providers()
    
    for provider_id in providers:
        print(f"\n🔍 Validating {provider_id}:")
        
        validation = service.validate_provider_configuration(provider_id)
        
        if validation['valid']:
            print("   ✅ Configuration is valid")
        else:
            print("   ❌ Configuration has errors:")
            for error in validation['errors']:
                print(f"      • {error}")
        
        if validation['warnings']:
            print("   ⚠️ Warnings:")
            for warning in validation['warnings']:
                print(f"      • {warning}")


def main():
    """Run all demonstrations."""
    print("🚀 Kitco Research AI - Unified Provider System Demo")
    print("=" * 60)
    
    try:
        # Basic registry operations
        demonstrate_provider_registry()
        
        # Service layer operations
        demonstrate_provider_service()
        
        # Hot swapping
        demonstrate_hot_swapping()
        
        # Model discovery
        demonstrate_model_discovery()
        
        # Validation
        demonstrate_validation()
        
        print("\n🎉 Demo completed successfully!")
        print("\nNext steps:")
        print("1. Try the CLI tool: python scripts/manage_providers.py list")
        print("2. Use the web API endpoints for integration")
        print("3. Add your own providers to config/providers.json")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you're running from the project root")
        print("2. Check that config/providers.json exists")
        print("3. Verify your environment variables are set")
        return False
    
    return True


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
