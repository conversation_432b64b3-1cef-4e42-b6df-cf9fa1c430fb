# 🤖 LM Studio + Docker Integration Guide

## 🎯 Overview

Your Docker container is now configured to automatically access LM Studio running on your host machine. This allows you to use local LLM models through LM Studio while running the application in a containerized environment.

## ✅ What's Configured

### **Docker Configuration**
- **Environment Variable**: `LM_STUDIO_URL=http://host.docker.internal:1234`
- **Host Access**: `extra_hosts: host.docker.internal=host-gateway`
- **Auto-Detection**: LM Studio provider automatically detected when available

### **Application Integration**
- **LLM Config Updated**: `src/kitco_research_ai/config/llm_config.py` now checks `LM_STUDIO_URL` environment variable
- **Provider Support**: LM Studio is included as a valid provider option
- **Model Auto-Detection**: Automatically detects available models from LM Studio

## 🚀 Quick Start

### **1. Start LM Studio on Host**
```bash
# Make sure LM Studio is running on localhost:1234
# Load your preferred model in LM Studio
```

### **2. Start Docker Container**
```bash
# Start the containerized application
./scripts/docker-start.sh

# Or manually
docker compose up -d
```

### **3. Test LM Studio Access**
```bash
# Run comprehensive tests including LM Studio connectivity
./scripts/docker-test.sh
```

### **4. Configure in Application**
1. Access the application at http://localhost:8765
2. Go to Settings → LLM Configuration
3. Select **Provider**: "LM Studio (Local)"
4. The URL will be automatically configured
5. Available models will be auto-detected

## 🔧 How It Works

### **Network Architecture**
```
┌─────────────────────────────────────────────────────────────┐
│                     Host Machine                            │
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   LM Studio     │    │     Docker      │                │
│  │  localhost:1234 │◄───│   Container     │                │
│  │                 │    │                 │                │
│  └─────────────────┘    │ host.docker.    │                │
│                         │ internal:1234   │                │
│                         └─────────────────┘                │
└─────────────────────────────────────────────────────────────┘
```

### **Connection Flow**
1. **Container starts** with `LM_STUDIO_URL=http://host.docker.internal:1234`
2. **Application checks** if LM Studio is available via availability function
3. **Models are detected** by querying `/v1/models` endpoint
4. **LLM requests** are routed through the configured URL

## 🧪 Testing

### **Automated Testing**
The `docker-test.sh` script includes specific LM Studio tests:

```bash
./scripts/docker-test.sh
```

**Tests performed:**
- ✅ Environment variable configuration
- ✅ Network connectivity to host machine
- ✅ LM Studio API accessibility
- ✅ Available models detection

### **Manual Testing**
```bash
# Test connectivity from container
docker exec kitco-research-ai-dev curl http://host.docker.internal:1234/v1/models

# Check available models
docker exec kitco-research-ai-dev curl -s http://host.docker.internal:1234/v1/models | jq '.data[].id'

# Verify environment variable
docker exec kitco-research-ai-dev printenv LM_STUDIO_URL
```

## 🔍 Troubleshooting

### **Common Issues**

#### **1. LM Studio Not Reachable**
```bash
# Check if LM Studio is running
curl http://localhost:1234/v1/models

# Check from container
docker exec kitco-research-ai-dev curl http://host.docker.internal:1234/v1/models
```

**Solutions:**
- Ensure LM Studio is running on port 1234
- Check LM Studio server settings
- Verify no firewall blocking port 1234

#### **2. No Models Available**
```bash
# Check loaded models in LM Studio
curl http://localhost:1234/v1/models | jq '.data[].id'
```

**Solutions:**
- Load a model in LM Studio interface
- Wait for model to fully load
- Check LM Studio logs for errors

#### **3. Connection Refused**
**Solutions:**
- Restart LM Studio
- Check LM Studio is listening on all interfaces (0.0.0.0:1234)
- Verify Docker host networking is working

### **Debug Commands**
```bash
# Check container networking
docker exec kitco-research-ai-dev ping host.docker.internal

# Test port connectivity
docker exec kitco-research-ai-dev nc -zv host.docker.internal 1234

# Check application logs
docker logs kitco-research-ai-dev | grep -i "lm.studio\|lmstudio"

# Verify environment
docker exec kitco-research-ai-dev env | grep LM_STUDIO
```

## ⚙️ Configuration Options

### **Environment Variables**
```bash
# In .env file or docker-compose.yml
LM_STUDIO_URL=http://host.docker.internal:1234

# Alternative ports if LM Studio runs on different port
LM_STUDIO_URL=http://host.docker.internal:8080
```

### **Application Settings**
In the web interface:
- **Provider**: LM Studio (Local)
- **Model**: Auto-detected from LM Studio
- **Temperature**: Configurable (default: 0.7)
- **Max Tokens**: Configurable (default: 30000)

## 🎯 Benefits

### **Local Processing**
- ✅ **Privacy**: Models run locally, no data sent to external APIs
- ✅ **Speed**: No network latency for API calls
- ✅ **Cost**: No per-token charges
- ✅ **Offline**: Works without internet connection

### **Containerized Deployment**
- ✅ **Portability**: Application runs consistently across environments
- ✅ **Isolation**: Dependencies contained and managed
- ✅ **Scalability**: Easy to deploy multiple instances
- ✅ **Development**: Hot reload and easy debugging

### **Best of Both Worlds**
- ✅ **Local LLMs**: Through LM Studio integration
- ✅ **Cloud APIs**: OpenAI, Anthropic when needed
- ✅ **Flexibility**: Switch between providers easily
- ✅ **Fallback**: Automatic fallback if LM Studio unavailable

## 📊 Performance Tips

### **LM Studio Optimization**
- Use GPU acceleration if available
- Allocate sufficient RAM for models
- Choose appropriate model size for your hardware
- Enable model caching in LM Studio

### **Container Optimization**
- Increase container memory limits if needed
- Use SSD storage for Docker volumes
- Monitor container resource usage
- Optimize network settings for local communication

## 🔄 Updates and Maintenance

### **Updating Models**
1. Load new models in LM Studio
2. Restart the Docker container (models auto-detected)
3. Select new model in application settings

### **Updating Configuration**
```bash
# Update environment variables
vim .env

# Restart container
./scripts/docker-stop.sh
./scripts/docker-start.sh
```

---

## 🎉 Success!

Your Docker container can now seamlessly access LM Studio running on your host machine! This provides the perfect combination of:

- **Local LLM processing** through LM Studio
- **Containerized application** for portability
- **Automatic detection** and configuration
- **Comprehensive testing** to ensure everything works

**Next Steps:**
1. Start LM Studio and load a model
2. Run `./scripts/docker-start.sh`
3. Test with `./scripts/docker-test.sh`
4. Configure LM Studio as your provider in the application settings

Enjoy the power of local LLMs with containerized deployment! 🚀
