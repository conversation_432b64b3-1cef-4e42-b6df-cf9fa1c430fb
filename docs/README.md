# Kitco Research AI

AI-powered research assistant with deep, iterative analysis using LLMs and web searches.

## 🚀 Quick Start

### Prerequisites
- Python 3.10 or higher
- Git

### 🌍 Cross-Platform Setup

**For detailed cross-platform instructions, see [Cross-Platform Setup Guide](CROSS_PLATFORM_SETUP.md)**

#### **Windows** 🪟
```cmd
# Initial setup (first time only)
setup.bat --development

# Start application
start.bat

# Fresh restart
restart.bat
```

#### **Linux/macOS** 🐧🍎
```bash
# Initial setup (first time only)
./setup --development

# Start application
./start

# Fresh restart
./restart
```

#### **Python Cross-Platform** 🐍
```bash
# Works on all platforms
python scripts/setup_cross_platform.py setup --development
python scripts/setup_cross_platform.py start
python scripts/setup_cross_platform.py restart
```

### Setup Process
The setup script will:
- ✅ Create virtual environment
- ✅ Install all Python dependencies
- ✅ Install Playwright browsers
- ✅ Initialize database
- ✅ Create configuration files

### 3. Access the Application

Once started, open your web browser and navigate to:
- **Default**: http://localhost:8765
- **Custom port**: http://localhost:YOUR_PORT

### 4. Quick Reference

For all available commands:
```bash
./help.sh
```

## 🔧 Configuration

### Environment Variables

The application uses a `.env` file for configuration. Key settings include:

```bash
# API Keys (uncomment and add your keys)
# OpenAI API Key - New format: sk-proj-... (164 characters)
# Get your key from: https://platform.openai.com/api-keys
# OPENAI_API_KEY=sk-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# Custom Endpoint API Key (e.g., OpenRouter, Azure OpenAI)
# CUSTOM_ENDPOINT=sk-or-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# BRAVE_API_KEY=BSA-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# SERP_API_KEY=xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
# GOOGLE_PSE_API_KEY=AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
# GOOGLE_PSE_ENGINE_ID=your-search-engine-id-here

# SearXNG Configuration
# SEARXNG_INSTANCE=http://localhost:8080
```

📖 **For detailed OpenAI setup instructions, see [OpenAI Integration Guide](OPENAI_INTEGRATION.md)**

### LLM Providers

The application supports multiple LLM providers:
- **OpenAI** (requires API key)
- **OpenAI-compatible endpoints**
- **Ollama** (local, requires Ollama running on localhost:11434)
- **LM Studio** (local, requires LM Studio running on localhost:1234)
- **vLLM** (local deployment)

Currently available providers: OpenAI, OpenAI Endpoint, vLLM

## 📁 Project Structure

```
kitco_research_ai/
├── venv/                   # Virtual environment
├── src/                    # Source code
│   └── kitco_research_ai/
├── data/                   # Application data and database
├── docs/                   # Documentation
├── scripts/                # Shell scripts
├── config/                 # Configuration files
├── .env                    # Environment configuration
├── app.py                  # Main application entry point
├── setup                   # Convenience setup script
├── start                   # Convenience start script
└── restart                 # Convenience restart script
```

## 🛠️ Cross-Platform Scripts

### Available Scripts

#### **Windows** 🪟
| Script | Purpose | Usage |
|--------|---------|-------|
| `setup.bat` | Initial setup (run once) | `setup.bat [--development\|--production]` |
| `start.bat` | Normal application start | `start.bat [port]` |
| `restart.bat` | Fresh restart with cleanup | `restart.bat [port]` |
| `scripts\setup.bat` | Direct setup script | `scripts\setup.bat [options]` |
| `scripts\start_app.bat` | Direct start script | `scripts\start_app.bat [port]` |
| `scripts\restart.bat` | Direct restart script | `scripts\restart.bat [port]` |

#### **Linux/macOS** 🐧🍎
| Script | Purpose | Usage |
|--------|---------|-------|
| `./setup` | Initial setup (run once) | `./setup [--development\|--production]` |
| `./start` | Normal application start | `./start [port]` |
| `./restart` | Fresh restart with cleanup | `./restart [port]` |
| `./scripts/setup.sh` | Direct setup script | `./scripts/setup.sh [options]` |
| `./scripts/start_app.sh` | Direct start script | `./scripts/start_app.sh [port]` |
| `./scripts/restart.sh` | Direct restart script | `./scripts/restart.sh [port]` |
| `./scripts/help.sh` | Show quick reference | `./scripts/help.sh` |

#### **Python Cross-Platform** 🐍
| Script | Purpose | Usage |
|--------|---------|-------|
| `python scripts/setup_cross_platform.py` | Cross-platform utility | `python scripts/setup_cross_platform.py [action] [options]` |

### Script Features

**setup.sh:**
- Checks Python version
- Creates virtual environment
- Installs all dependencies
- Sets up Playwright browsers
- Initializes database
- Creates configuration files

**start_app.sh:**
- Validates setup
- Checks port availability
- Starts application safely

**restart.sh:**
- Kills existing processes
- Cleans temporary files
- Validates environment
- Performs fresh start

## 🛠️ Development

### Installing New Dependencies

For detailed dependency management, see `config/README-requirements.md`.

**Development Dependencies:**
```bash
source .venv/bin/activate
pip install new-dev-package
# Add to config/requirements-dev.txt with proper category and comment
```

**Production Dependencies:**
```bash
source .venv/bin/activate
pip install new-package
# Add to config/requirements.txt and config/requirements-prod.txt
# with proper category and comment
```

**Quick Reference:**
- `config/requirements.txt` - Main comprehensive requirements
- `config/requirements-dev.txt` - Development tools and testing
- `config/requirements-prod.txt` - Minimal production dependencies

### Database

The application uses SQLite database stored in `data/ldr.db`. The database is automatically initialized on first run.

## 🔍 Features

- **Deep Research**: Iterative analysis with multiple search rounds
- **Multiple Search Engines**: DuckDuckGo, Google, Brave, SearXNG support
- **Web Scraping**: Playwright-based content extraction
- **Document Processing**: PDF, text, and web content analysis
- **Real-time Interface**: Flask-SocketIO web interface
- **Research History**: Persistent storage of research sessions

## 🚨 Troubleshooting

### Port Already in Use
If the default port 8765 is in use, specify a different port:
```bash
./start 8766
```

### Missing Dependencies
If you encounter import errors, reinstall dependencies:

```bash
source .venv/bin/activate
pip install -r config/requirements.txt
```

### Playwright Issues
If web scraping fails, reinstall Playwright browsers:

```bash
source .venv/bin/activate
playwright install
```

## 📝 Notes

- The application will show warnings about Ollama and LM Studio not being available - this is normal if you haven't installed these local LLM servers
- For full functionality, configure at least one LLM provider (OpenAI recommended for beginners)
- Search functionality works without API keys using DuckDuckGo, but additional search engines require API keys

## 🎯 Next Steps

1. **Configure LLM Provider**: Add your OpenAI API key to `.env` file
2. **Add Search APIs**: Configure additional search engines for better results
3. **Explore Features**: Try the research interface and experiment with different queries
4. **Customize Settings**: Use the web interface to adjust research parameters

Happy researching! 🔬

## 📚 Additional Documentation

- **[CROSS_PLATFORM_SETUP.md](CROSS_PLATFORM_SETUP.md)** - Comprehensive cross-platform setup guide
- **[OPENAI_INTEGRATION.md](OPENAI_INTEGRATION.md)** - Complete OpenAI API setup and usage guide
- **[PRODUCTION.md](PRODUCTION.md)** - Production deployment guide
- **[SECURITY.md](SECURITY.md)** - Security configuration and best practices
- **[SECURE_SECRETS_MANAGEMENT.md](SECURE_SECRETS_MANAGEMENT.md)** - Comprehensive secrets management
