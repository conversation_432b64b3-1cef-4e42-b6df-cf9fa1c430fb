# Branching Strategy - Simple Guide

## 🎯 Quick Reference: Which Branch Should I Use?

| **What are you doing?** | **Use this branch** | **Branch from** | **Merge to** |
|------------------------|-------------------|----------------|-------------|
| 🆕 Adding a new feature | `feature/feature-name` | `develop` | `develop` |
| 🐛 Fixing a bug | `feature/fix-bug-name` | `develop` | `develop` |
| 🚀 Preparing a release | `release/v1.0.0` | `develop` | `main` + `develop` |
| 🚨 Emergency production fix | `hotfix/fix-name` | `main` | `main` + `develop` |
| 📖 Just reading/exploring | `main` or `develop` | - | - |

---

## 🌳 Our 5 Branch Types Explained

### 1. **`main`** - The Production Branch 🏭
**What it is:** The "live" version of your app that users see
- ✅ **Always works** - Never broken code here
- ✅ **Always deployable** - Ready to go live anytime
- ❌ **Never code directly here** - Only merge from other branches

**Think of it as:** The final product on the store shelf

### 2. **`develop`** - The Integration Branch 🔧
**What it is:** Where all new features come together for testing
- ✅ **Latest features** - All new work gets merged here first
- ✅ **Testing ground** - Make sure everything works together
- ⚠️ **May have bugs** - It's okay, we fix them before release

**Think of it as:** The workshop where you assemble everything

### 3. **`feature/*`** - Feature Branches 🛠️
**What it is:** Your personal workspace for building something new
- ✅ **One feature per branch** - Keep things focused
- ✅ **Safe to experiment** - Won't break anything else
- ✅ **Easy to throw away** - If it doesn't work out

**Examples:**
- `feature/user-login`
- `feature/research-dashboard`
- `feature/fix-search-bug`

**Think of it as:** Your personal workbench

### 4. **`release/*`** - Release Preparation Branches 📦
**What it is:** Final testing and polishing before going live
- ✅ **Feature freeze** - No new features, only bug fixes
- ✅ **Final testing** - Make sure everything is perfect
- ✅ **Version preparation** - Update version numbers, docs

**Examples:**
- `release/v1.0.0`
- `release/v2.1.0`

**Think of it as:** Quality control before shipping

### 5. **`hotfix/*`** - Emergency Fix Branches 🚨
**What it is:** Quick fixes for critical problems in production
- ✅ **Urgent only** - For serious bugs that can't wait
- ✅ **Fast track** - Skip normal process for speed
- ✅ **Small changes** - Keep it minimal and focused

**Examples:**
- `hotfix/security-vulnerability`
- `hotfix/critical-crash`

**Think of it as:** Emergency repairs

---

## 🚀 Step-by-Step Workflows

### 🆕 Adding a New Feature

1. **Start from develop:**
   ```bash
   git checkout develop
   git pull origin develop  # Get latest changes
   ```

2. **Create your feature branch:**
   ```bash
   git checkout -b feature/my-awesome-feature
   ```

3. **Work on your feature:**
   ```bash
   # Make your changes
   git add .
   git commit -m "Add awesome feature"
   git push origin feature/my-awesome-feature
   ```

4. **Create Pull Request:**
   - From: `feature/my-awesome-feature`
   - To: `develop`
   - Get it reviewed and approved

5. **Clean up:**
   ```bash
   git checkout develop
   git branch -d feature/my-awesome-feature  # Delete local branch
   ```

### 🚀 Releasing to Production

1. **Create release branch:**
   ```bash
   git checkout develop
   git pull origin develop
   git checkout -b release/v1.0.0
   ```

2. **Final preparations:**
   ```bash
   # Update version numbers
   # Fix any last-minute bugs
   # Update documentation
   git commit -m "Prepare release v1.0.0"
   git push origin release/v1.0.0
   ```

3. **Create Pull Requests:**
   - **First:** `release/v1.0.0` → `main` (for production)
   - **Second:** `release/v1.0.0` → `develop` (to keep in sync)

4. **After merge, tag the release:**
   ```bash
   git checkout main
   git pull origin main
   git tag v1.0.0
   git push origin v1.0.0
   ```

### 🚨 Emergency Hotfix

1. **Create hotfix branch:**
   ```bash
   git checkout main
   git pull origin main
   git checkout -b hotfix/critical-bug-fix
   ```

2. **Fix the problem:**
   ```bash
   # Make minimal fix
   git add .
   git commit -m "Fix critical bug"
   git push origin hotfix/critical-bug-fix
   ```

3. **Create Pull Requests:**
   - **First:** `hotfix/critical-bug-fix` → `main` (urgent fix)
   - **Second:** `hotfix/critical-bug-fix` → `develop` (keep in sync)

---

## 🛡️ Branch Protection Rules

### `main` Branch
- ❌ **No direct pushes** - Must use Pull Requests
- ✅ **Requires review** - At least 1 approval needed
- ✅ **Must pass CI** - All tests must pass
- ✅ **Auto-deploy** - Automatically deploys to production

### `develop` Branch
- ❌ **No direct pushes** - Must use Pull Requests
- ✅ **Requires review** - At least 1 approval needed
- ✅ **Must pass CI** - All tests must pass

### `feature/*` Branches
- ✅ **Direct pushes OK** - It's your workspace
- ✅ **No review required** - For your own commits
- ⚠️ **Must pass CI** - Before merging to develop

---

## 🎨 Branch Naming Conventions

### Feature Branches
```
feature/short-description
feature/user-authentication
feature/research-dashboard
feature/fix-search-bug
feature/add-export-functionality
```

### Release Branches
```
release/v1.0.0
release/v2.1.0
release/v1.5.2
```

### Hotfix Branches
```
hotfix/security-patch
hotfix/critical-crash-fix
hotfix/database-connection-issue
```

**Rules:**
- Use lowercase with hyphens
- Be descriptive but concise
- Include ticket numbers if you have them: `feature/TICKET-123-user-login`

---

## 🤔 Common Questions & Scenarios

### "I'm new to the project, where do I start?"
1. Clone the repository
2. Checkout `develop` branch: `git checkout develop`
3. Create your feature branch: `git checkout -b feature/my-first-feature`
4. Start coding!

### "I found a small bug while working on a feature"
- **If it's related to your feature:** Fix it in your feature branch
- **If it's unrelated:** Create a separate `feature/fix-bug-name` branch

### "The production site is broken!"
1. **Assess severity:**
   - **Critical (site down, security issue):** Use hotfix process
   - **Minor (typo, small bug):** Use normal feature process

### "I accidentally committed to the wrong branch"
```bash
# If you haven't pushed yet:
git reset --soft HEAD~1  # Undo last commit, keep changes
git stash                # Save your changes
git checkout correct-branch
git stash pop           # Apply your changes
git add . && git commit -m "Your message"
```

### "My feature branch is behind develop"
```bash
git checkout develop
git pull origin develop
git checkout feature/my-feature
git merge develop  # Or use: git rebase develop
```

### "I need to update my Pull Request"
```bash
# Make your changes
git add .
git commit -m "Address review feedback"
git push origin feature/my-feature  # Updates the PR automatically
```

---

## 🚦 CI/CD Integration

Our automated systems work with these branches:

### Automatic Testing
- **All branches:** Run tests on every push
- **Pull Requests:** Must pass all tests before merging

### Automatic Deployment
- **`main` branch:** Automatically deploys to production
- **`develop` branch:** Automatically deploys to staging environment

### Security Scanning
- **Weekly:** Automatic dependency security scans
- **Every PR:** Code security analysis

---

## 📋 Checklist Templates

### ✅ Before Creating a Feature Branch
- [ ] I'm on the `develop` branch
- [ ] I've pulled the latest changes: `git pull origin develop`
- [ ] I have a clear idea of what I'm building
- [ ] I've chosen a good branch name

### ✅ Before Creating a Pull Request
- [ ] My code works locally
- [ ] I've tested my changes
- [ ] I've written/updated tests if needed
- [ ] I've updated documentation if needed
- [ ] My commits have clear messages
- [ ] I've pushed my branch: `git push origin feature/my-feature`

### ✅ Before Merging to Main (Release)
- [ ] All features are complete and tested
- [ ] Version number is updated
- [ ] Documentation is updated
- [ ] Release notes are prepared
- [ ] Staging environment testing is complete
- [ ] Team has approved the release

---

## 🆘 Getting Help

### If you're stuck:
1. **Check this document** - Most answers are here
2. **Ask a teammate** - They've probably faced the same issue
3. **Check Git status:** `git status` shows what's happening
4. **Check current branch:** `git branch` shows where you are

### Useful Git Commands
```bash
# See all branches
git branch -a

# See current status
git status

# See recent commits
git log --oneline -10

# Undo last commit (keep changes)
git reset --soft HEAD~1

# Discard all local changes
git checkout -- .

# Switch branches
git checkout branch-name

# Create and switch to new branch
git checkout -b new-branch-name
```

---

## 📚 Summary

**Remember the golden rules:**
1. **`main`** = Production (always working)
2. **`develop`** = Integration (latest features)
3. **`feature/*`** = Your workspace (safe to experiment)
4. **Always use Pull Requests** for `main` and `develop`
5. **Test before you merge**
6. **Keep branches focused** - one feature per branch

**When in doubt:** Start from `develop`, create a `feature/` branch, and ask for help! 🚀