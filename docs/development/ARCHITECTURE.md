# Kitco Research AI - Architecture Documentation

## Overview

Kitco Research AI has been restructured into a robust, maintainable architecture following modern software engineering principles. This document outlines the new architecture and its benefits.

## Architecture Principles

### 1. **Dependency Injection**
- All components use dependency injection for loose coupling
- Services are registered in a central container
- Easy to test and mock dependencies

### 2. **Service Layer Pattern**
- Business logic is encapsulated in service classes
- Clear separation between presentation, business, and data layers
- Services implement well-defined interfaces

### 3. **Configuration Management**
- Centralized configuration with multiple sources
- Environment variables override database settings
- Database settings override file configuration
- File configuration overrides defaults

### 4. **Error Handling**
- Custom exception hierarchy for different error types
- Structured error responses with error codes
- Comprehensive logging and monitoring

### 5. **Testability**
- Comprehensive test suite with unit, integration, and E2E tests
- Mock services for isolated testing
- Test fixtures and utilities

## Directory Structure

```
src/kitco_research_ai/
├── core/                    # Core application components
│   ├── application.py       # Application factory
│   ├── container.py         # Dependency injection container
│   └── exceptions.py        # Custom exception hierarchy
├── services/                # Service layer
│   ├── interfaces.py        # Service interfaces
│   ├── database_service.py  # Database operations
│   ├── search_service.py    # Search operations
│   ├── report_service.py    # Report generation
│   ├── logging_service.py   # Logging management
│   ├── health_service.py    # Health monitoring
│   └── validation_service.py # Input validation
├── config/                  # Configuration management
│   └── manager.py           # Centralized config manager
├── api/                     # Public API
│   └── factory.py           # Factory functions
├── web/                     # Web interface (existing)
├── advanced_search_system/  # Search strategies (existing)
├── web_search_engines/      # Search engines (existing)
└── utilities/               # Utility functions (existing)

tests/                       # Test suite
├── unit/                    # Unit tests
├── integration/             # Integration tests
├── e2e/                     # End-to-end tests
└── conftest.py              # Test configuration
```

## Key Components

### Core Application (`core/`)

#### Application Factory
- `create_application()` - Creates and initializes the application
- Registers all services with dependency injection
- Handles application lifecycle

#### Dependency Container
- Manages service registration and resolution
- Supports singleton and transient services
- Thread-safe implementation

#### Exception Hierarchy
- `KRAError` - Base exception for all application errors
- Specific exceptions for different error types
- Structured error information with codes and details

### Service Layer (`services/`)

#### Configuration Service
- Centralized configuration management
- Multiple configuration sources with priority
- Validation of configuration values
- Environment variable support

#### Database Service
- Database connection management
- Health monitoring
- Schema migrations
- Backup functionality

#### Search Service
- Search operation coordination
- Engine management
- Query validation
- Result processing

#### Report Service
- Report generation in multiple formats
- Template management
- Metadata handling
- Storage integration

#### Health Service
- Comprehensive system health monitoring
- Component-specific health checks
- Performance metrics
- Resource monitoring

#### Validation Service
- Input validation and sanitization
- Schema-based validation
- Security filtering
- Type conversion

### Configuration Management (`config/`)

#### Configuration Manager
- Hierarchical configuration loading
- Environment variable integration
- File-based configuration
- Runtime configuration updates

## Benefits of the New Architecture

### 1. **Maintainability**
- Clear separation of concerns
- Modular design with well-defined interfaces
- Easy to understand and modify

### 2. **Testability**
- Dependency injection enables easy mocking
- Comprehensive test suite
- Isolated unit tests

### 3. **Scalability**
- Service-oriented architecture
- Easy to add new features
- Horizontal scaling support

### 4. **Reliability**
- Comprehensive error handling
- Health monitoring
- Graceful degradation

### 5. **Security**
- Input validation and sanitization
- Secure configuration management
- Error information filtering

### 6. **Observability**
- Structured logging
- Health metrics
- Performance monitoring

## Usage Examples

### Creating the Application

```python
from kitco_research_ai import create_application

# Create and initialize application
app = create_application()

# Get services
config_service = app.get_service(IConfigService)
search_service = app.get_service(ISearchService)
```

### Using Factory Functions

```python
from kitco_research_ai import create_search_system, create_report_generator

# Create search system
search_system = create_search_system(strategy_name="iterdrag")

# Create report generator
report_generator = create_report_generator()
```

### Configuration Management

```python
import sys
sys.path.insert(0, 'src')
from config.config_loader import config

# Get configuration values
port = config.get("application.technical_settings.port", 8765)
debug = config.get("application.technical_settings.debug", False)

# Get editorial settings (for UI)
editorial_settings = config.get_editorial_settings()

# Get technical settings (for admin)
technical_settings = config.get_technical_settings()
```

## Migration Guide

### For Existing Code

The new architecture maintains backward compatibility through wrapper functions:

```python
# Old way (still works)
from kitco_research_ai import get_llm, get_search, get_advanced_search_system

# New way (recommended)
from kitco_research_ai import create_search_system, create_application
```

### For New Development

Use the new service-oriented approach:

```python
# Create application
app = create_application()

# Get services
search_service = app.get_service(ISearchService)
config_service = app.get_service(IConfigService)

# Use services
results = search_service.search("query")
config_value = config_service.get("key")
```

## Testing

### Running Tests

```bash
# Run all tests
pytest

# Run unit tests only
pytest tests/unit/

# Run with coverage
pytest --cov=src/kitco_research_ai

# Run specific test markers
pytest -m "unit"
pytest -m "integration"
```

### Test Structure

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test component interactions
- **End-to-End Tests**: Test complete workflows

## Health Monitoring

### CLI Health Check

```bash
# Comprehensive health check
python -m kitco_research_ai health

# Check specific component
python -m kitco_research_ai health --component database
```

### Programmatic Health Check

```python
app = create_application()
health_service = app.get_service(IHealthService)

# Get health status
status = health_service.check_health()
print(f"Overall status: {status['overall_status']}")
```

## Configuration

### Environment Variables

All configuration can be overridden with environment variables using the `KRA_` prefix:

```bash
export KRA_APP_PORT=9000
export KRA_DATABASE_PATH="/custom/path/ldr.db"
export KRA_LLM_TEMPERATURE=0.8
```

### Configuration Files

TOML configuration files are supported:

```toml
[app]
port = 8765
debug = false

[database]
path = "data/ldr.db"

[llm]
provider = "ollama"
model = "gemma:latest"
temperature = 0.7
```

## Performance Considerations

### Service Lifecycle
- Services are created as singletons by default
- Database connections are pooled
- Configuration is cached

### Memory Management
- Proper cleanup in application shutdown
- Resource monitoring
- Memory leak detection

### Caching
- Configuration caching
- Search result caching
- Database query optimization

## Security

### Input Validation
- All user inputs are validated and sanitized
- Schema-based validation
- XSS and injection prevention

### Configuration Security
- Sensitive configuration in environment variables
- No hardcoded secrets
- Secure defaults

### Error Handling
- No sensitive information in error messages
- Structured error responses
- Audit logging

## Future Enhancements

### Planned Features
- Metrics collection and export
- Distributed tracing
- Configuration hot-reloading
- Plugin system
- API rate limiting

### Extension Points
- Custom search strategies
- Additional report formats
- External service integrations
- Custom validation rules
