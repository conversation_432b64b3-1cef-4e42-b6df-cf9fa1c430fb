# 🚀 Git Branching Quick Reference Card

## 🎯 Which Branch Do I Use?

| **Task** | **Branch Type** | **Command** |
|----------|----------------|-------------|
| New feature | `feature/name` | `git checkout develop && git checkout -b feature/my-feature` |
| Bug fix | `feature/fix-name` | `git checkout develop && git checkout -b feature/fix-bug` |
| Release prep | `release/v1.0.0` | `git checkout develop && git checkout -b release/v1.0.0` |
| Emergency fix | `hotfix/name` | `git checkout main && git checkout -b hotfix/critical-fix` |

## 🌳 Branch Hierarchy

```
main (production) ←── hotfix/emergency-fix
  ↑
  └── release/v1.0.0 ←── develop (integration)
                           ↑
                           ├── feature/user-login
                           ├── feature/dashboard
                           └── feature/bug-fix
```

## ⚡ Common Commands

### Starting Work
```bash
# Get latest changes
git checkout develop
git pull origin develop

# Create feature branch
git checkout -b feature/my-awesome-feature

# Work and commit
git add .
git commit -m "Add awesome feature"
git push origin feature/my-awesome-feature
```

### Creating Pull Request
1. Push your branch: `git push origin feature/my-feature`
2. Go to GitHub/GitLab
3. Create PR: `feature/my-feature` → `develop`
4. Get review and approval
5. Merge!

### After Merge - Cleanup
```bash
git checkout develop
git pull origin develop
git branch -d feature/my-feature  # Delete local branch
```

## 🚨 Emergency Hotfix
```bash
git checkout main
git pull origin main
git checkout -b hotfix/critical-fix
# Fix the issue
git add . && git commit -m "Fix critical issue"
git push origin hotfix/critical-fix
# Create PR to main AND develop
```

## 🛡️ Branch Rules

- **main**: No direct pushes, requires PR + review + CI
- **develop**: No direct pushes, requires PR + review + CI  
- **feature/***: Direct pushes OK, but CI must pass before merge

## 🆘 Help Commands

```bash
git status          # What's happening?
git branch -a       # Show all branches
git log --oneline   # Recent commits
git checkout --     # Discard changes
```

## 📞 When to Ask for Help

- Merge conflicts you can't resolve
- Accidentally committed to wrong branch
- Need to undo something
- CI/CD pipeline failing
- Not sure which branch to use

**Remember: It's better to ask than to break something!** 🤝
