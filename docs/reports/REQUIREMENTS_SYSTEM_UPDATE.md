# Requirements System Update - Migration Guide

## 🎯 Overview

The Kitco Research AI project has been updated with a comprehensive, multi-environment requirements system that provides better dependency management, security, and development experience.

## 🆕 What's New

### **New Requirements Files**

1. **`config/requirements.txt`** - Main comprehensive requirements
   - All dependencies organized by category
   - Detailed comments explaining each package
   - Suitable for development and production

2. **`config/requirements-dev.txt`** - Development environment
   - Includes all production dependencies
   - Adds testing frameworks (pytest, coverage)
   - Adds code quality tools (black, mypy, pylint)
   - Adds debugging and profiling tools
   - Adds documentation tools (sphinx)

3. **`config/requirements-prod.txt`** - Production environment
   - Minimal dependencies for security
   - Excludes development and testing tools
   - Optimized for production deployment

4. **`config/README-requirements.md`** - Comprehensive guide
   - Installation instructions for each environment
   - Maintenance procedures
   - Security considerations
   - Docker integration examples

### **Updated Scripts**

- **`scripts/setup.sh`** - Now supports environment selection
- **`scripts/restart.sh`** - Auto-detects environment and uses appropriate requirements
- **`scripts/status.sh`** - Shows environment type and requirements file status
- **`scripts/update_requirements.sh`** - Migration script for existing installations
- **`scripts/help.sh`** - Updated with new commands and options

### **Docker Support**

- **`Dockerfile`** - Production-optimized multi-stage build
- **`Dockerfile.dev`** - Development environment with all tools
- **`docker-compose.yml`** - Multi-environment setup with profiles
- **`.dockerignore`** - Optimized for faster builds

### **CI/CD Integration**

- **`.github/workflows/ci.yml`** - Comprehensive CI/CD pipeline
- Automated testing across Python versions
- Code quality checks (black, mypy, pylint)
- Security scanning (bandit, safety, pip-audit)
- Docker build and test
- Automated dependency updates

## 🚀 Migration Instructions

### **For Existing Users**

1. **Automatic Migration** (Recommended):
   ```bash
   ./scripts/update_requirements.sh
   ```
   This script will:
   - Backup your existing virtual environment
   - Let you choose your preferred environment
   - Create a new virtual environment
   - Install appropriate dependencies
   - Test the installation

2. **Manual Migration**:
   ```bash
   # Backup existing environment
   mv .venv .venv.backup
   
   # Choose your environment and run setup
   ./scripts/setup.sh --development    # For development
   ./scripts/setup.sh --production     # For production
   ./scripts/setup.sh                  # For standard
   ```

### **For New Users**

Simply run the setup script with your preferred environment:

```bash
# Development (recommended for local work)
./scripts/setup.sh --development

# Production (minimal dependencies)
./scripts/setup.sh --production

# Standard (all features, no dev tools)
./scripts/setup.sh
```

## 🎯 Environment Comparison

| Feature | Development | Standard | Production |
|---------|-------------|----------|------------|
| **Core Dependencies** | ✅ | ✅ | ✅ |
| **AI/ML Libraries** | ✅ | ✅ | ✅ |
| **Web Framework** | ✅ | ✅ | ✅ |
| **Document Processing** | ✅ | ✅ | ✅ |
| **Testing Framework** | ✅ | ❌ | ❌ |
| **Code Formatting** | ✅ | ❌ | ❌ |
| **Type Checking** | ✅ | ❌ | ❌ |
| **Debugging Tools** | ✅ | ❌ | ❌ |
| **Documentation Tools** | ✅ | ❌ | ❌ |
| **Security Scanning** | ✅ | ❌ | ❌ |
| **Package Count** | ~170 | ~160 | ~140 |

## 🔧 Usage Examples

### **Development Workflow**
```bash
# Setup development environment
./scripts/setup.sh --development

# Start development server
./scripts/start_app.sh

# Run tests
pytest

# Format code
black src/

# Type checking
mypy src/

# Security audit
pip-audit
```

### **Production Deployment**
```bash
# Setup production environment
./scripts/setup.sh --production

# Start production server
./scripts/start_app.sh

# Or use Docker
docker-compose --profile prod up
```

### **Docker Development**
```bash
# Development with Docker
docker-compose --profile dev up

# Production with Docker
docker-compose --profile prod up
```

## 🔒 Security Improvements

1. **Minimal Production Dependencies**: Production environment excludes unnecessary packages
2. **Automated Security Scanning**: CI/CD pipeline includes security audits
3. **Dependency Vulnerability Checks**: Regular scanning with pip-audit and safety
4. **Docker Security**: Multi-stage builds and non-root users

## 📊 Benefits

### **For Developers**
- ✅ Comprehensive development tools
- ✅ Automated code quality checks
- ✅ Easy testing and debugging
- ✅ Hot-reload development environment

### **For Production**
- ✅ Minimal attack surface
- ✅ Faster deployment
- ✅ Reduced resource usage
- ✅ Better security posture

### **For DevOps**
- ✅ Multi-environment Docker support
- ✅ Comprehensive CI/CD pipeline
- ✅ Automated dependency management
- ✅ Security scanning integration

## 🆘 Troubleshooting

### **Common Issues**

1. **"Import Error" after migration**:
   ```bash
   ./scripts/update_requirements.sh
   ```

2. **"Command not found" errors**:
   ```bash
   chmod +x scripts/*.sh
   ```

3. **Docker build failures**:
   ```bash
   docker system prune
   docker-compose build --no-cache
   ```

4. **Virtual environment issues**:
   ```bash
   rm -rf .venv
   ./scripts/setup.sh --development
   ```

### **Getting Help**

- **Quick Reference**: `./scripts/help.sh`
- **Status Check**: `./scripts/status.sh`
- **Requirements Guide**: `config/README-requirements.md`
- **Complete Documentation**: `docs/README.md`

## 📝 Changelog

### **Added**
- Multi-environment requirements system
- Docker support with development and production configurations
- Comprehensive CI/CD pipeline
- Automated security scanning
- Migration script for existing installations
- Detailed documentation and guides

### **Changed**
- Setup script now supports environment selection
- Scripts auto-detect environment type
- Documentation updated with new system information

### **Improved**
- Security posture with minimal production dependencies
- Development experience with comprehensive tooling
- Deployment options with Docker support
- Maintenance procedures with automated tools

---

**Ready to migrate?** Run `./scripts/update_requirements.sh` to get started! 🚀
