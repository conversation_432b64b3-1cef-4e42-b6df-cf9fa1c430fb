# Unified Provider System - Single Source of Truth

The Unified Provider System provides centralized management for all LLM providers, their configurations, and capabilities. This system serves as the single source of truth for provider management, enabling easy maintenance, hot swapping, and dynamic provider discovery.

## 🎯 Key Features

- **Centralized Configuration**: Single JSON file defining all providers
- **Hot Swapping**: Runtime provider switching without restart
- **Secure Key Management**: Integration with existing secrets system
- **Dynamic Discovery**: Auto-detection of local providers and models
- **Validation**: Provider availability and configuration validation
- **Easy Maintenance**: Simple JSON structure for adding new providers
- **Web API**: RESTful endpoints for provider management
- **CLI Tools**: Command-line utilities for administration

## 📁 File Structure

```
config/
├── providers.json                          # Central provider definitions
src/kitco_research_ai/
├── config/
│   └── provider_registry.py               # Core registry implementation
└── web/
    ├── services/
    │   └── provider_service.py             # Web service layer
    └── routes/
        └── providers.py                    # API endpoints
scripts/
└── manage_providers.py                    # CLI management tool
```

## 🔧 Configuration

### Provider Definition Structure

Each provider in `config/providers.json` follows this structure:

```json
{
  "provider_id": {
    "name": "Human-readable name",
    "description": "Provider description",
    "type": "cloud|local|hybrid",
    "enabled": true,
    "priority": 1,
    "hidden": false,
    
    "api": {
      "base_url": "https://api.example.com/v1",
      "base_url_env": "PROVIDER_URL",
      "base_url_db": "llm.provider.url",
      "api_key_env": "PROVIDER_API_KEY",
      "api_key_db": "llm.provider.api_key",
      "api_key_required": true
    },
    
    "models": {
      "default": "model-name",
      "available": ["model1", "model2"],
      "dynamic_discovery": true,
      "discovery_endpoint": "/v1/models"
    },
    
    "capabilities": {
      "chat": true,
      "streaming": true,
      "function_calling": true,
      "vision": false,
      "max_tokens": 128000,
      "context_window": 128000
    },
    
    "parameters": {
      "temperature": {
        "default": 0.7,
        "min": 0.0,
        "max": 2.0
      }
    }
  }
}
```

### Environment Variables

The system supports multiple ways to configure API keys and endpoints:

```bash
# OpenAI
OPENAI_API_KEY=sk-your-openai-key

# Custom endpoints (OpenRouter, Azure, etc.)
CUSTOM_ENDPOINT=sk-or-your-custom-key
CUSTOM_ENDPOINT_URL=https://openrouter.ai/api/v1

# Local providers
OLLAMA_URL=http://localhost:11434
LM_STUDIO_URL=http://localhost:1234
```

## 🚀 Usage

### Python API

```python
from src.kitco_research_ai.config.provider_registry import get_provider_registry
from src.kitco_research_ai.web.services.provider_service import get_provider_service

# Get registry instance
registry = get_provider_registry()

# List available providers
providers = registry.get_available_providers()

# Get provider details
provider = registry.get_provider('openai')

# Get API key securely
api_key = registry.get_provider_api_key('openai')

# Get available models
models = registry.get_provider_models('ollama', refresh=True)

# Use service layer for web operations
service = get_provider_service()

# Switch provider (hot swap)
success, message = service.set_current_provider('openai')

# Switch model
success, message = service.set_current_model('gpt-4o')
```

### CLI Management

```bash
# List all providers
python scripts/manage_providers.py list

# Switch provider
python scripts/manage_providers.py switch openai

# List models for a provider
python scripts/manage_providers.py models ollama

# Check health of all providers
python scripts/manage_providers.py health

# Validate provider configuration
python scripts/manage_providers.py validate openai
```

### Web API

```bash
# List providers
GET /api/providers/

# Get current provider
GET /api/providers/current

# Switch provider
POST /api/providers/current
{
  "provider": "openai"
}

# Get provider models
GET /api/providers/ollama/models?refresh=true

# Check health
GET /api/providers/health

# Validate configuration
POST /api/providers/openai/validate
```

## 🔐 Security

### API Key Resolution Order

1. **Secure Secrets Manager** (if available)
2. **Environment Variables**
3. **Database Settings**
4. **Default/Placeholder Values**

### Key Storage Options

- **Environment Variables**: `.env` files (development)
- **Encrypted Storage**: Production secrets management
- **Database Settings**: User-configurable keys
- **Runtime Configuration**: Temporary overrides

## 🏗️ Adding New Providers

### 1. Update providers.json

Add your provider configuration to `config/providers.json`:

```json
{
  "my_provider": {
    "name": "My Custom Provider",
    "description": "Custom LLM provider",
    "type": "cloud",
    "enabled": true,
    "priority": 10,
    
    "api": {
      "base_url": "https://api.myprovider.com/v1",
      "api_key_env": "MY_PROVIDER_API_KEY",
      "api_key_db": "llm.my_provider.api_key"
    },
    
    "models": {
      "default": "my-model-v1",
      "available": ["my-model-v1", "my-model-v2"]
    },
    
    "capabilities": {
      "chat": true,
      "streaming": false,
      "max_tokens": 4000
    }
  }
}
```

### 2. Add Environment Variable

```bash
MY_PROVIDER_API_KEY=your-api-key-here
```

### 3. Test Configuration

```bash
python scripts/manage_providers.py validate my_provider
python scripts/manage_providers.py switch my_provider
```

## 🔄 Hot Swapping

The system supports runtime provider switching without application restart:

```python
# Switch provider
service = get_provider_service()
success, message = service.set_current_provider('ollama')

# Switch model
success, message = service.set_current_model('llama3.2:latest')
```

Changes take effect immediately for new requests.

## 📊 Monitoring

### Health Checks

The system automatically monitors provider health:

- **Cloud Providers**: API key validation
- **Local Providers**: Endpoint connectivity
- **Model Discovery**: Available model detection

### Status Information

```python
# Check all providers
health_status = service.check_all_providers_health()

# Get detailed provider info
details = service.get_provider_details('openai')
```

## 🛠️ Troubleshooting

### Common Issues

1. **Provider Not Available**
   - Check API key configuration
   - Verify endpoint connectivity
   - Review provider logs

2. **Model Not Found**
   - Refresh model list: `refresh=true`
   - Check provider documentation
   - Verify model availability

3. **Configuration Errors**
   - Validate configuration: `validate` command
   - Check required fields
   - Review JSON syntax

### Debug Commands

```bash
# Detailed provider information
python scripts/manage_providers.py list

# Health check with details
python scripts/manage_providers.py health

# Configuration validation
python scripts/manage_providers.py validate <provider>
```

## 🔮 Future Enhancements

- **Provider Plugins**: Dynamic provider loading
- **Load Balancing**: Multiple provider instances
- **Failover**: Automatic provider switching
- **Metrics**: Usage and performance tracking
- **UI Dashboard**: Web-based management interface

## 📚 Related Documentation

- [LLM Configuration](./development/LLM_CONFIGURATION.md)
- [OpenAI Integration](./development/OPENAI_INTEGRATION.md)
- [Security Best Practices](./SECURITY.md)
- [API Documentation](./API_REFERENCE.md)
