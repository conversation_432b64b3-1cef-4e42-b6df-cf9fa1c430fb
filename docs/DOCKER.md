# 🐳 Docker Deployment Guide

Complete guide for containerizing and deploying Kitco Research AI with Docker.

## 📋 Table of Contents

- [Overview](#overview)
- [Prerequisites](#prerequisites)
- [Quick Start](#quick-start)
- [Architecture](#architecture)
- [Configuration](#configuration)
- [Scripts](#scripts)
- [Troubleshooting](#troubleshooting)
- [Production Deployment](#production-deployment)

## 🎯 Overview

The Kitco Research AI Docker setup provides:

- **🔍 Integrated SearXNG**: Self-hosted search engine for web research
- **🏗️ Multi-environment**: Separate development and production configurations
- **📊 Health monitoring**: Automatic health checks for all services
- **💾 Data persistence**: Volumes for data, logs, and research outputs
- **🔒 Network security**: Isolated container communication
- **⚡ Easy deployment**: One-command startup and testing

## 🛠️ Prerequisites

### Required Software
- **Docker**: Version 20.10+ with Docker Compose V2
- **Git**: For cloning the repository
- **Bash**: For running setup scripts (Linux/macOS/WSL)

### System Requirements
- **RAM**: Minimum 4GB, recommended 8GB+
- **Storage**: 10GB free space for images and data
- **Network**: Internet access for pulling images and search functionality

### Verification
```bash
# Check Docker installation
docker --version
docker compose version

# Verify Docker is running
docker info
```

## 🚀 Quick Start

### 1. Clone and Setup
```bash
git clone <repository-url>
cd kitco_research_ai
```

### 2. Start Development Environment
```bash
# Start with integrated SearXNG
./scripts/docker-start.sh

# Or start with logs visible
./scripts/docker-start.sh -l
```

### 3. Access Services
- **Main Application**: http://localhost:8765
- **SearXNG Search**: http://localhost:8080

### 4. Test Everything Works
```bash
./scripts/docker-test.sh
```

### 5. Stop When Done
```bash
./scripts/docker-stop.sh
```

## 🏗️ Architecture

### Container Services

```mermaid
graph TB
    subgraph "Docker Network"
        A[Kitco Research AI<br/>Port 8765] --> B[SearXNG<br/>Port 8080]
    end

    E[Host Machine] --> A
    E --> B

    F[Data Volumes] --> A
    G[Search Data] --> B
```

### Service Details

| Service | Purpose | Port | Profile |
|---------|---------|------|---------|
| **kitco-research-ai-dev** | Main application (development) | 8765 | dev |
| **kitco-research-ai-prod** | Main application (production) | 8765 | prod |
| **searxng** | Self-hosted search engine | 8080 | all |

## ⚙️ Configuration

### Environment Variables

Create `.env` file in project root:

```bash
# Application Environment
ENVIRONMENT=development
DEBUG=true

# SearXNG Configuration
SEARXNG_SECRET_KEY=your-secret-key-here
SEARXNG_URL=http://searxng:8080

# API Keys
OPENAI_API_KEY=your-openai-api-key
SERPAPI_API_KEY=your-serpapi-key

# Local LLM Providers (auto-configured for Docker)
LM_STUDIO_URL=http://host.docker.internal:1234

# Database (SQLite - current setup)
DATABASE_PATH=data/ldr.db
```

### Docker Compose Profiles

```bash
# Development (default)
docker-compose --profile dev up

# Production
docker-compose --profile prod up
```

### Volume Mounts

| Volume | Purpose | Development | Production |
|--------|---------|-------------|------------|
| `./data` | Application database | ✅ | ✅ |
| `./research_outputs` | Generated reports | ✅ | ✅ |
| `./logs` | Application logs | ✅ | ✅ |
| `.` (source code) | Hot reload | ✅ | ❌ |

## 🔧 Scripts

### docker-start.sh
Comprehensive startup script with options:

```bash
# Basic usage
./scripts/docker-start.sh [OPTIONS]

# Options
-e, --env ENVIRONMENT    # dev|prod (default: dev)
-p, --pull              # Pull latest images
-b, --build             # Force rebuild
-l, --logs              # Show logs after start
-f, --foreground        # Run in foreground
-h, --help              # Show help

# Examples
./scripts/docker-start.sh -e prod -p    # Production with latest images
./scripts/docker-start.sh -b -l         # Rebuild and show logs
```

### docker-stop.sh
Safe shutdown with cleanup options:

```bash
# Basic usage
./scripts/docker-stop.sh [OPTIONS]

# Options
-e, --env ENVIRONMENT    # dev|prod|all (default: all)
-v, --volumes           # Remove volumes (DATA LOSS!)
-i, --images            # Remove application images
-f, --force             # Force stop (kill)
-h, --help              # Show help

# Examples
./scripts/docker-stop.sh -e dev         # Stop only dev containers
./scripts/docker-stop.sh -v             # Stop and remove all data
```

### docker-test.sh
Comprehensive testing suite:

```bash
./scripts/docker-test.sh

# Tests performed:
# ✅ Container status and health
# ✅ Network connectivity
# ✅ SearXNG functionality
# ✅ Application endpoints
# ✅ Environment variables
# ✅ Volume mounts
# ✅ Resource usage
```

## 🤖 LM Studio Integration

The Docker setup includes automatic support for LM Studio running on your host machine.

### **How It Works**
- Container uses `host.docker.internal:1234` to reach LM Studio on the host
- Environment variable `LM_STUDIO_URL` is automatically configured
- LM Studio provider is auto-detected when available

### **Setup Steps**
1. **Start LM Studio** on your host machine (default port 1234)
2. **Load a model** in LM Studio
3. **Start the Docker container**:
   ```bash
   ./scripts/docker-start.sh
   ```
4. **Test connectivity**:
   ```bash
   ./scripts/docker-test.sh
   ```

### **Configuration**
In your application settings, select:
- **Provider**: LM Studio (Local)
- **Model**: Will auto-detect available models
- **URL**: Automatically configured as `http://host.docker.internal:1234`

### **Troubleshooting LM Studio**
```bash
# Test if LM Studio is reachable from container
docker exec kitco-research-ai-dev curl http://host.docker.internal:1234/v1/models

# Check available models
docker exec kitco-research-ai-dev curl http://host.docker.internal:1234/v1/models | jq '.data[].id'

# Verify environment variable
docker exec kitco-research-ai-dev printenv LM_STUDIO_URL
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Check what's using the port
lsof -i :8765
lsof -i :8080

# Stop conflicting services
./scripts/docker-stop.sh
```

#### 2. SearXNG Not Accessible
```bash
# Check SearXNG container
docker logs kitco-searxng

# Test SearXNG directly
curl http://localhost:8080

# Restart SearXNG
docker restart kitco-searxng
```

#### 3. Application Can't Reach SearXNG
```bash
# Check network connectivity
docker exec kitco-research-ai-dev curl http://searxng:8080

# Verify environment variable
docker exec kitco-research-ai-dev printenv SEARXNG_URL
```

#### 4. Permission Issues
```bash
# Fix volume permissions
sudo chown -R $USER:$USER ./data ./logs ./research_outputs

# Or run with proper user
docker-compose down
docker-compose up
```

### Debug Commands

```bash
# View all container logs
docker-compose logs

# View specific service logs
docker-compose logs searxng
docker-compose logs kitco-research-ai-dev

# Execute commands in container
docker exec -it kitco-research-ai-dev bash

# Check container resource usage
docker stats

# Inspect container configuration
docker inspect kitco-research-ai-dev
```

## 🚀 Production Deployment

### 1. Production Setup
```bash
# Start production environment
./scripts/docker-start.sh -e prod -p
```

### 2. Security Considerations
- Change default passwords in `.env`
- Use proper SSL certificates
- Configure firewall rules
- Regular security updates
- Monitor container logs

### 3. Performance Optimization
- Monitor resource usage
- Optimize container resource limits
- Scale containers as needed
- Use SSD storage for volumes

### 4. Backup Strategy
```bash
# Backup SearXNG data volume
docker run --rm -v kitco_research_ai_searxng_data:/data -v $(pwd):/backup alpine tar czf /backup/searxng_backup.tar.gz /data

# Backup application data
tar czf backup_$(date +%Y%m%d).tar.gz ./data ./research_outputs ./logs
```

### 5. Updates and Maintenance
```bash
# Update to latest version
git pull
./scripts/docker-start.sh -e prod -p -b

# Clean up old images
docker image prune -f

# Monitor disk usage
docker system df
```

## 📊 Monitoring

### Health Checks
All services include health checks:
- **Main App**: `/health` endpoint
- **SearXNG**: Root page accessibility

### Log Management
```bash
# View logs with timestamps
docker-compose logs -t

# Follow logs in real-time
docker-compose logs -f

# Limit log output
docker-compose logs --tail=100
```

### Resource Monitoring
```bash
# Real-time stats
docker stats

# Container resource limits
docker inspect kitco-research-ai-prod | grep -A 10 "Resources"
```

---

## 🆘 Need Help?

- **Test your setup**: `./scripts/docker-test.sh`
- **View logs**: `docker-compose logs`
- **Check status**: `docker-compose ps`
- **Get help**: `./scripts/docker-start.sh --help`

For more detailed troubleshooting, see the [main documentation](README.md).
