# ============================================================================
# Kitco Research AI - Production Requirements
# ============================================================================
# Minimal production dependencies with security and performance optimizations.
# This file excludes development, testing, and debugging tools.
#
# Installation: pip install -r config/requirements-prod.txt
# ============================================================================

# ============================================================================
# CORE WEB FRAMEWORK & SERVER
# ============================================================================
Flask==3.1.1
flask-cors==6.0.0
Flask-SocketIO==5.5.1
Flask-WTF==1.2.2
Werkzeug==3.1.3
WTForms==3.2.1
Jinja2==3.1.6
MarkupSafe==3.0.2
itsdangerous==2.2.0
blinker==1.9.0
click==8.2.1

# ============================================================================
# REAL-TIME COMMUNICATION
# ============================================================================
python-socketio==5.13.0
python-engineio==4.12.1
simple-websocket==1.1.0
bidict==0.23.1
wsproto==1.2.0

# ============================================================================
# AI & MACHINE LEARNING
# ============================================================================
# LangChain Framework
langchain==0.3.25
langchain-community==0.3.24
langchain-core==0.3.60
langchain-ollama==0.3.3
langchain-openai==0.3.17
langchain-text-splitters==0.3.8
langsmith==0.3.42

# OpenAI & LLM Providers
openai==1.68.2
ollama==0.4.9
tiktoken==0.9.0

# Machine Learning & NLP
torch==2.7.0
transformers==4.52.3
sentence-transformers==4.1.0
tokenizers==0.21.1
huggingface-hub==0.32.2
safetensors==0.5.3

# Vector Search & Embeddings
faiss-cpu==1.11.0

# Scientific Computing
numpy<2.2
scipy==1.15.3
pandas==2.2.3
scikit-learn==1.6.1

# Natural Language Processing
nltk==3.9.1
langdetect==1.0.9
python-iso639==2025.2.18

# ============================================================================
# DATABASE & DATA PERSISTENCE
# ============================================================================
SQLAlchemy==2.0.41
alembic==1.16.1
Mako==1.3.10

# ============================================================================
# WEB SCRAPING & SEARCH
# ============================================================================
playwright==1.52.0
beautifulsoup4==4.13.4
lxml==5.4.0
lxml_html_clean==0.4.2
html5lib==1.1
soupsieve==2.7
duckduckgo_search==8.0.2
google_search_results==2.4.2
wikipedia==1.4.0
feedparser==6.0.11
arxiv==2.2.0

# ============================================================================
# HTTP CLIENTS & NETWORKING
# ============================================================================
requests==2.32.3
requests-toolbelt==1.0.0
httpx==0.28.1
httpcore==1.0.9
httpx-sse==0.4.0
aiohttp==3.12.2
aiosignal==1.3.2
aiohappyeyeballs==2.6.1
urllib3==2.4.0
certifi==2025.4.26
charset-normalizer==3.4.2
idna==3.10

# ============================================================================
# DOCUMENT PROCESSING
# ============================================================================
pypdf==5.5.0
pdfplumber==0.11.6
pdfminer.six==20250327
pypdfium2==4.30.1
unstructured==0.17.2
unstructured-client==0.35.0
filetype==1.2.0
python-magic==0.4.27
python-oxmsg==0.0.2
jusText==3.0.2
RapidFuzz==3.13.0
regex==2024.11.6

# ============================================================================
# CONFIGURATION & ENVIRONMENT
# ============================================================================
dynaconf==3.2.11
python-dotenv==1.1.0
pydantic<2.10.0
pydantic-settings==2.9.1
pydantic_core==2.33.2
PyYAML==6.0.2
toml==0.10.2

# ============================================================================
# LOGGING & MONITORING
# ============================================================================
loguru==0.7.3
colorlog==6.9.0

# ============================================================================
# ASYNC & CONCURRENCY
# ============================================================================
anyio==4.9.0
nest-asyncio==1.6.0
greenlet==3.2.2

# ============================================================================
# CACHING & PERFORMANCE
# ============================================================================
backoff==2.2.1
tenacity==9.1.2
methodtools==0.4.7

# ============================================================================
# UTILITIES & HELPERS
# ============================================================================
aiofiles==24.1.0
filelock~=3.12.2
fsspec==2025.5.1
platformdirs==4.3.8
attrs==25.3.0
dataclasses-json==0.6.7
marshmallow==3.26.1
orjson==3.10.18
jsonpatch==1.33
jsonpointer==3.0.0
six==1.17.0
emoji==2.14.1
webencodings==0.5.1
xmltodict==0.14.2
psutil~=6.0.0
distro==1.9.0
zstandard==0.23.0
yarl==1.20.0
multidict==6.4.4
frozenlist==1.6.0
propcache==0.3.1
cryptography==45.0.3
cffi==1.17.1

# ============================================================================
# SEARCH & INDEXING
# ============================================================================
elasticsearch==8.14.0
elastic-transport==8.17.1

# ============================================================================
# TYPE CHECKING & ANNOTATIONS
# ============================================================================
typing_extensions==4.13.2
annotated-types==0.7.0
typing-inspect==0.9.0
typing-inspection==0.4.1
mypy_extensions==1.1.0

# ============================================================================
# SCIENTIFIC & MATHEMATICAL LIBRARIES
# ============================================================================
joblib==1.5.1
threadpoolctl==3.6.0
tqdm==4.67.1
networkx==3.4.2
contourpy==1.3.2
cycler==0.12.1
fonttools==4.58.0
kiwisolver==1.4.8
mpmath==1.3.0
pyparsing==3.2.3
narwhals==1.41.0
olefile==0.47

# ============================================================================
# LEGACY & COMPATIBILITY
# ============================================================================
sgmllib3k==1.0.0
setuptools==80.8.0
importlib_resources==6.5.2
wrapt==1.17.2
wirerope==1.0.0

# ============================================================================
# TIME & DATE HANDLING
# ============================================================================
python-dateutil==2.9.0.post0
pytz==2025.2
tzdata==2025.2

# ============================================================================
# BROWSER AUTOMATION SUPPORT
# ============================================================================
pyee==13.0.0

# ============================================================================
# ENCODING & CHARACTER DETECTION
# ============================================================================
chardet==5.2.0

# ============================================================================
# HTTP/WEB PROTOCOL SUPPORT
# ============================================================================
h11==0.16.0
sniffio==1.3.1

# ============================================================================
# SPECIALIZED UTILITIES
# ============================================================================
hf-xet==1.1.2
primp==0.15.0

# ============================================================================
# PRODUCTION OPTIMIZATIONS
# ============================================================================
# Note: Consider these additional optimizations for production:
# - Use gunicorn or uwsgi instead of Flask's development server
# - Consider using Redis for caching and session storage
# - Use a proper database like PostgreSQL instead of SQLite for high load
# - Set up proper logging aggregation (ELK stack, etc.)
# - Use environment-specific configuration management
# ============================================================================
