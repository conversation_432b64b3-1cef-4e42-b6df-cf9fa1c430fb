# ============================================================================
# Python 3.13 Specific Requirements Overrides
# ============================================================================
# This file contains package version overrides and exclusions for Python 3.13
# Use this when the main requirements.txt has compatibility issues
#
# Usage:
#   pip install -r config/requirements.txt -r config/requirements-python-3.13.txt
#
# Or use the setup script which automatically detects Python version
# ============================================================================

# ============================================================================
# PACKAGES TO EXCLUDE (Not compatible with Python 3.13)
# ============================================================================

# Data profiling package - not yet compatible with Python 3.13
# Alternative: Use pandas-profiling when it becomes available
# ydata-profiling==4.16.1  # EXCLUDED - No Python 3.13 support yet

# ============================================================================
# VERSION OVERRIDES (Use different versions for Python 3.13)
# ============================================================================

# If any packages need specific versions for Python 3.13, list them here
# Example:
# some-package>=1.0.0,<2.0.0  # Use newer version range for Python 3.13

# ============================================================================
# ALTERNATIVE PACKAGES (Replacements for incompatible packages)
# ============================================================================

# Alternative data profiling (when ydata-profiling is not available)
sweetviz>=2.1.4  # Alternative EDA tool - COMPATIBLE WITH PYTHON 3.13
# pandas-profiling>=3.6.6  # If available for Python 3.13

# ============================================================================
# NOTES
# ============================================================================
# 
# This file is automatically used by the setup scripts when Python 3.13 is detected.
# 
# To check which packages are incompatible:
#   python scripts/setup/check_compatibility.py
#
# To update this file:
#   1. Test installation with new Python version
#   2. Add incompatible packages to exclusion list
#   3. Find alternative packages or newer versions
#   4. Update setup scripts to handle the new version
#
# ============================================================================
