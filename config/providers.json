{"//": "═══════════════════════════════════════════════════════════════════════════════", "version": "1.0.0", "last_updated": "2025-06-20", "providers": {"openai": {"name": "OpenAI", "description": "Official OpenAI API with GPT models", "type": "cloud", "enabled": true, "priority": 1, "api": {"base_url": "https://api.openai.com/v1", "api_key_env": "OPENAI_API_KEY", "api_key_db": "llm.openai.api_key", "headers": {"Content-Type": "application/json"}}, "models": {"default": "gpt-4o-mini", "available": ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-3.5-turbo"]}, "capabilities": {"chat": true, "streaming": true, "function_calling": true, "vision": true, "max_tokens": 128000, "context_window": 128000}, "parameters": {"temperature": {"default": 0.7, "min": 0.0, "max": 2.0}, "max_tokens": {"default": 4000, "max": 128000}}}, "custom_endpoint": {"name": "Custom Endpoint", "description": "OpenRouter, Azure OpenAI, or other OpenAI-compatible APIs", "type": "cloud", "enabled": true, "priority": 2, "api": {"base_url_env": "CUSTOM_ENDPOINT_URL", "base_url_db": "llm.custom_endpoint.url", "base_url_default": "https://api.x.ai/v1", "api_key_env": "CUSTOM_ENDPOINT", "api_key_db": "llm.custom_endpoint.api_key", "headers": {"Content-Type": "application/json"}}, "models": {"default": "grok-beta", "available": ["grok-beta", "grok-vision-beta"], "dynamic_discovery": true, "discovery_endpoint": "/models"}, "capabilities": {"chat": true, "streaming": true, "function_calling": true, "vision": false, "max_tokens": 128000, "context_window": 128000}, "parameters": {"temperature": {"default": 0.7, "min": 0.0, "max": 2.0}, "max_tokens": {"default": 4000, "max": 128000}}}, "ollama": {"name": "<PERSON><PERSON><PERSON> (Local)", "description": "Local Ollama server for privacy and offline use", "type": "local", "enabled": true, "priority": 3, "api": {"base_url_env": "OLLAMA_URL", "base_url_db": "llm.ollama.url", "base_url_default": "http://localhost:11434", "api_key_required": false, "health_check_endpoint": "/api/tags"}, "models": {"default": "llama3.2:latest", "available": [], "dynamic_discovery": true, "discovery_endpoint": "/api/tags"}, "capabilities": {"chat": true, "streaming": true, "function_calling": false, "vision": false, "max_tokens": 32768, "context_window": 32768}, "parameters": {"temperature": {"default": 0.7, "min": 0.0, "max": 2.0}, "max_tokens": {"default": 2000, "max": 32768}}}, "lmstudio": {"name": "LM Studio (Local)", "description": "Local LM Studio server with OpenAI-compatible API", "type": "local", "enabled": true, "priority": 4, "api": {"base_url_env": "LM_STUDIO_URL", "base_url_db": "llm.lmstudio.url", "base_url_default": "http://localhost:1234", "api_key_required": false, "api_key_placeholder": "lm-studio", "health_check_endpoint": "/v1/models"}, "models": {"default": "auto", "available": [], "dynamic_discovery": true, "discovery_endpoint": "/v1/models"}, "capabilities": {"chat": true, "streaming": true, "function_calling": false, "vision": false, "max_tokens": 32768, "context_window": 32768}, "parameters": {"temperature": {"default": 0.7, "min": 0.0, "max": 2.0}, "max_tokens": {"default": 2000, "max": 32768}}}, "vllm": {"name": "vLLM (High Performance)", "description": "vLLM server for high-performance local inference", "type": "local", "enabled": false, "priority": 5, "hidden": true, "api": {"base_url_env": "VLLM_URL", "base_url_db": "llm.vllm.url", "base_url_default": "http://localhost:8000", "api_key_required": false}, "models": {"default": "meta-llama/Llama-2-7b-chat-hf", "available": []}, "capabilities": {"chat": true, "streaming": true, "function_calling": false, "vision": false, "max_tokens": 32768, "context_window": 32768}, "parameters": {"temperature": {"default": 0.7, "min": 0.0, "max": 2.0}, "max_tokens": {"default": 2000, "max": 32768}}}}, "settings": {"default_provider": "openai", "fallback_provider": "openai", "auto_discovery": true, "health_check_timeout": 3.0, "retry_attempts": 2, "cache_duration": 300}, "validation": {"required_fields": ["name", "description", "type", "api"], "provider_types": ["cloud", "local", "hybrid"], "capability_fields": ["chat", "streaming", "function_calling", "vision"]}}